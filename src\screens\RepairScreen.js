import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  TextInput,
  Alert,
  Modal,
  ActivityIndicator,
  Image,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import { repairAPI, uploadImage } from '../services/api';
import API_CONFIG from '../services/api';

const RepairScreen = ({ navigation }) => {
  const [selectedCategory, setSelectedCategory] = useState('');
  const [selectedLocation, setSelectedLocation] = useState('');
  const [description, setDescription] = useState('');
  const [contactInfo, setContactInfo] = useState('');
  const [showCategoryModal, setShowCategoryModal] = useState(false);
  const [showLocationModal, setShowLocationModal] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [uploadedImages, setUploadedImages] = useState([]);

  const categories = [
    '電器設備',
    '水電設施',
    '門窗設備',
    '空調設備',
    '網路設備',
    '其他設備',
  ];

  const locations = [
    'A棟-機械工程系二館',
    'B棟-機械工程系一館',
    'C棟-電子工程系',
    'D棟-創新育成中心',
    'E棟-電漿研究中心、貴重儀器中心',
    'F棟-數位內容多媒體技術研發中心、資訊管理系、電機工程系、工程學院大樓、資訊網路工程系',
    'G棟-人文暨設計學院大樓、文化創意與數位媒體設計系',
    'P棟-多媒體與遊戲發展科學系、應用外語系、雙語中心',
    'H棟-半導體工程系',
    'N棟-教學整合智慧教室、法民紀念館、藝文中心、校本部、總務處、環安室、教務處、稽核處、國際暨兩岸合作處、教學發展中心、綜合型階梯教室、研發處、三創中心',
    'L棟-資訊圖書處',
    'K棟-財務金融系、企業管理系、專案管理中心、數位行銷暨跨境商務系、管理學院大樓、工業管理系',
    'W棟-第一女生宿舍',
    'S棟-教室、微縮教室、健康中心、衛生保健組、進修部',
    'T棟-教室、T520階梯教室、T720階梯教室、觀光休閒系、專業職能證照中心、推廣教育中心、校安中心、咖啡吧',
    'M棟-男生宿舍、第二女生宿舍',
    'U棟-學務處、校友服務中心、諮商輔導暨職涯發展中心、課指組、通識教育中心、生輔組、體育室、演藝廳、天空操場、體育館、桌球教室、舞蹈教室、體適能教室、防身術教室',
  ];

  const handleSubmitRepair = async () => {
    // 表單驗證
    if (!selectedCategory) {
      Alert.alert('提醒', '請選擇報修類別');
      return;
    }
    if (!selectedLocation) {
      Alert.alert('提醒', '請選擇報修地點');
      return;
    }
    if (!description.trim()) {
      Alert.alert('提醒', '請填寫問題描述');
      return;
    }
    if (!contactInfo.trim()) {
      Alert.alert('提醒', '請填寫聯絡方式');
      return;
    }

    setIsSubmitting(true);

    try {
      // 準備提交的數據
      const repairData = {
        category: selectedCategory,
        location: selectedLocation,
        description: description.trim(),
        contactInfo: contactInfo.trim(),
        images: uploadedImages,
        timestamp: new Date().toISOString(),
        status: 'pending', // 待處理
      };

      console.log('提交報修數據:', repairData);

      // 調用 API 提交報修申請
      const response = await repairAPI.submitRepair(repairData);

      if (response.success) {
        Alert.alert(
          '報修成功',
          `您的報修申請已提交！\n報修編號：${response.repairId || '系統生成中'}\n我們會盡快處理您的申請。`,
          [
            {
              text: '確定',
              onPress: () => {
                // 清空表單
                setSelectedCategory('');
                setSelectedLocation('');
                setDescription('');
                setContactInfo('');
                setUploadedImages([]);
                navigation.goBack();
              },
            },
          ]
        );
      } else {
        throw new Error(response.message || '提交失敗');
      }
    } catch (error) {
      console.error('提交報修失敗:', error);

      let errorMessage = '提交失敗，請稍後再試';
      let showMockOption = false;

      if (error.message.includes('網路')) {
        errorMessage = '網路連線異常，請檢查網路設定後重試';
      } else if (error.message.includes('超時')) {
        errorMessage = '請求超時，請檢查網路連線';
      } else if (error.message.includes('404')) {
        errorMessage = 'API 端點不存在\n\n可能原因：\n• 後端路由配置錯誤\n• API 端點路徑不正確\n• 後端服務器未正確啟動';
        showMockOption = true;
      } else if (error.message.includes('502') || error.message.includes('503')) {
        errorMessage = 'ngrok 隧道或後端服務器不可用\n\n請檢查：\n• ngrok 隧道是否正常運行\n• 後端服務器是否啟動\n• URL 是否正確';
      } else if (error.message.includes('Network request failed')) {
        errorMessage = '網路連線失敗\n\n請檢查：\n• ngrok URL 是否正確\n• 網路連線是否正常\n• 防火牆設定';
      } else if (error.message.includes('ngrok')) {
        errorMessage = 'ngrok 相關錯誤\n\n' + error.message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      const alertButtons = [
        {
          text: '重試',
          onPress: () => handleSubmitRepair(),
        },
        {
          text: '取消',
          style: 'cancel',
        },
      ];

      // 如果是後端連接問題，提供模擬模式選項
      if (showMockOption) {
        alertButtons.unshift({
          text: '使用模擬模式',
          onPress: () => {
            // 啟用模擬模式並重新提交
            API_CONFIG.MOCK_MODE = true;
            Alert.alert(
              '模擬模式已啟用',
              '現在將使用模擬數據進行測試，請重新提交報修申請。',
              [{ text: '確定' }]
            );
          },
        });
      }

      Alert.alert('提交失敗', errorMessage, alertButtons);
    } finally {
      setIsSubmitting(false);
    }
  };

  // 處理圖片選擇
  const handleImagePicker = async () => {
    try {
      // 請求相機權限
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('權限不足', '需要相簿權限才能上傳照片');
        return;
      }

      Alert.alert(
        '選擇照片',
        '請選擇照片來源',
        [
          {
            text: '相機',
            onPress: () => pickImageFromCamera(),
          },
          {
            text: '相簿',
            onPress: () => pickImageFromLibrary(),
          },
          {
            text: '取消',
            style: 'cancel',
          },
        ]
      );
    } catch (error) {
      console.error('圖片選擇錯誤:', error);
      Alert.alert('錯誤', '無法開啟圖片選擇器');
    }
  };

  // 從相機拍照
  const pickImageFromCamera = async () => {
    try {
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('權限不足', '需要相機權限才能拍照');
        return;
      }

      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        await handleImageUpload(result.assets[0].uri);
      }
    } catch (error) {
      console.error('相機拍照錯誤:', error);
      Alert.alert('錯誤', '拍照失敗');
    }
  };

  // 從相簿選擇
  const pickImageFromLibrary = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        await handleImageUpload(result.assets[0].uri);
      }
    } catch (error) {
      console.error('相簿選擇錯誤:', error);
      Alert.alert('錯誤', '選擇照片失敗');
    }
  };

  // 處理圖片上傳
  const handleImageUpload = async (imageUri) => {
    try {
      setIsSubmitting(true);

      // 這裡可以調用上傳 API，目前先存儲本地 URI
      // const uploadedUrl = await uploadImage(imageUri);

      // 暫時使用本地 URI，實際應用中應該上傳到服務器
      const newImage = {
        uri: imageUri,
        id: Date.now().toString(),
      };

      setUploadedImages(prev => [...prev, newImage]);
      Alert.alert('成功', '照片已添加');
    } catch (error) {
      console.error('圖片上傳錯誤:', error);
      Alert.alert('錯誤', '照片上傳失敗');
    } finally {
      setIsSubmitting(false);
    }
  };

  // 移除圖片
  const removeImage = (imageId) => {
    setUploadedImages(prev => prev.filter(img => img.id !== imageId));
  };

  const renderCategoryModal = () => (
    <Modal
      animationType="slide"
      transparent={true}
      visible={showCategoryModal}
      onRequestClose={() => setShowCategoryModal(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>選擇報修類別</Text>
            <TouchableOpacity onPress={() => setShowCategoryModal(false)}>
              <Ionicons name="close" size={24} color="#333" />
            </TouchableOpacity>
          </View>
          <ScrollView style={styles.modalList}>
            {categories.map((category, index) => (
              <TouchableOpacity
                key={index}
                style={styles.modalItem}
                onPress={() => {
                  setSelectedCategory(category);
                  setShowCategoryModal(false);
                }}
              >
                <Text style={styles.modalItemText}>{category}</Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      </View>
    </Modal>
  );

  const renderLocationModal = () => (
    <Modal
      animationType="slide"
      transparent={true}
      visible={showLocationModal}
      onRequestClose={() => setShowLocationModal(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>選擇報修地點</Text>
            <TouchableOpacity onPress={() => setShowLocationModal(false)}>
              <Ionicons name="close" size={24} color="#333" />
            </TouchableOpacity>
          </View>
          <ScrollView style={styles.modalList}>
            {locations.map((location, index) => (
              <TouchableOpacity
                key={index}
                style={styles.modalItem}
                onPress={() => {
                  setSelectedLocation(location);
                  setShowLocationModal(false);
                }}
              >
                <Text style={styles.modalItemText}>{location}</Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      </View>
    </Modal>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Ionicons name="chevron-back" size={24} color="#333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>設備報修</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content}>
        {/* Form */}
        <View style={styles.formContainer}>
          <Text style={styles.formTitle}>設備報修申請</Text>
          
          {/* Category Selection */}
          <View style={styles.formGroup}>
            <Text style={styles.label}>報修類別 *</Text>
            <TouchableOpacity
              style={styles.selector}
              onPress={() => setShowCategoryModal(true)}
            >
              <Text style={[
                styles.selectorText,
                !selectedCategory && styles.placeholderText
              ]}>
                {selectedCategory || '請選擇報修類別'}
              </Text>
              <Ionicons name="chevron-down" size={20} color="#666" />
            </TouchableOpacity>
          </View>

          {/* Location Selection */}
          <View style={styles.formGroup}>
            <Text style={styles.label}>報修地點 *</Text>
            <TouchableOpacity
              style={styles.selector}
              onPress={() => setShowLocationModal(true)}
            >
              <Text style={[
                styles.selectorText,
                !selectedLocation && styles.placeholderText
              ]}>
                {selectedLocation || '請選擇報修地點'}
              </Text>
              <Ionicons name="chevron-down" size={20} color="#666" />
            </TouchableOpacity>
          </View>

          {/* Description */}
          <View style={styles.formGroup}>
            <Text style={styles.label}>問題描述 *</Text>
            <TextInput
              style={styles.textArea}
              placeholder="請詳細描述設備問題，包括具體位置、故障現象等"
              multiline
              numberOfLines={6}
              value={description}
              onChangeText={setDescription}
              textAlignVertical="top"
            />
          </View>

          {/* Contact Info */}
          <View style={styles.formGroup}>
            <Text style={styles.label}>聯絡方式 *</Text>
            <TextInput
              style={styles.textInput}
              placeholder="請填寫您的聯絡電話或Email"
              value={contactInfo}
              onChangeText={setContactInfo}
            />
          </View>

          {/* Photo Upload */}
          <View style={styles.formGroup}>
            <Text style={styles.label}>上傳照片（選填）</Text>
            <TouchableOpacity
              style={styles.photoUpload}
              onPress={handleImagePicker}
              disabled={isSubmitting}
            >
              <Ionicons name="camera" size={24} color="#666" />
              <Text style={styles.photoUploadText}>點擊上傳問題照片</Text>
            </TouchableOpacity>

            {/* 顯示已上傳的圖片 */}
            {uploadedImages.length > 0 && (
              <View style={styles.uploadedImagesContainer}>
                <Text style={styles.uploadedImagesTitle}>已上傳的照片：</Text>
                <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                  {uploadedImages.map((image) => (
                    <View key={image.id} style={styles.imageContainer}>
                      <Image source={{ uri: image.uri }} style={styles.uploadedImage} />
                      <TouchableOpacity
                        style={styles.removeImageButton}
                        onPress={() => removeImage(image.id)}
                      >
                        <Ionicons name="close-circle" size={20} color="#ff4444" />
                      </TouchableOpacity>
                    </View>
                  ))}
                </ScrollView>
              </View>
            )}
          </View>

          {/* Submit Button */}
          <TouchableOpacity
            style={[styles.submitButton, isSubmitting && styles.submitButtonDisabled]}
            onPress={handleSubmitRepair}
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <View style={styles.submitButtonContent}>
                <ActivityIndicator size="small" color="#fff" />
                <Text style={[styles.submitButtonText, { marginLeft: 8 }]}>提交中...</Text>
              </View>
            ) : (
              <Text style={styles.submitButtonText}>提交報修申請</Text>
            )}
          </TouchableOpacity>

          {/* Note */}
          <View style={styles.noteContainer}>
            <Text style={styles.noteTitle}>注意事項：</Text>
            <Text style={styles.noteText}>
              • 請詳細描述問題，以便維修人員快速定位
            </Text>
            <Text style={styles.noteText}>
              • 緊急情況請直接聯絡總務處：02-1234-5678
            </Text>
            <Text style={styles.noteText}>
              • 我們會在24小時內回覆您的報修申請
            </Text>
            <Text style={styles.noteText}>
              • 目前使用{API_CONFIG.MOCK_MODE ? '模擬模式' : 'ngrok 後端'}進行數據提交
            </Text>
            {!API_CONFIG.MOCK_MODE && (
              <Text style={styles.noteText}>
                • 後端 API: {API_CONFIG.BASE_URL}
              </Text>
            )}
          </View>
        </View>
      </ScrollView>

      {/* Modals */}
      {renderCategoryModal()}
      {renderLocationModal()}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 50,
    paddingBottom: 15,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  backButton: {
    padding: 5,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  placeholder: {
    width: 34,
  },
  content: {
    flex: 1,
  },
  formContainer: {
    backgroundColor: '#fff',
    margin: 16,
    borderRadius: 8,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  formTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 20,
    textAlign: 'center',
  },
  formGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  selector: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    backgroundColor: '#f8f8f8',
  },
  selectorText: {
    fontSize: 14,
    color: '#333',
  },
  placeholderText: {
    color: '#999',
  },
  textInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 14,
    color: '#333',
    backgroundColor: '#f8f8f8',
  },
  textArea: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 14,
    color: '#333',
    backgroundColor: '#f8f8f8',
    minHeight: 100,
  },
  photoUpload: {
    borderWidth: 2,
    borderColor: '#ddd',
    borderStyle: 'dashed',
    borderRadius: 8,
    paddingVertical: 20,
    alignItems: 'center',
    backgroundColor: '#f8f8f8',
  },
  photoUploadText: {
    fontSize: 14,
    color: '#666',
    marginTop: 8,
  },
  submitButton: {
    backgroundColor: '#364873',
    paddingVertical: 15,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 10,
  },
  submitButtonDisabled: {
    backgroundColor: '#999',
    opacity: 0.7,
  },
  submitButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  submitButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  uploadedImagesContainer: {
    marginTop: 12,
  },
  uploadedImagesTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
    marginBottom: 8,
  },
  imageContainer: {
    position: 'relative',
    marginRight: 12,
  },
  uploadedImage: {
    width: 80,
    height: 80,
    borderRadius: 8,
  },
  removeImageButton: {
    position: 'absolute',
    top: -8,
    right: -8,
    backgroundColor: '#fff',
    borderRadius: 10,
  },
  noteContainer: {
    marginTop: 20,
    padding: 16,
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: '#364873',
  },
  noteTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  noteText: {
    fontSize: 12,
    color: '#666',
    lineHeight: 18,
    marginBottom: 4,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: '#fff',
    borderRadius: 12,
    width: '80%',
    maxHeight: '60%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  modalTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  modalList: {
    maxHeight: 300,
  },
  modalItem: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  modalItemText: {
    fontSize: 14,
    color: '#333',
  },
});

export default RepairScreen;
