import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

const BookOrderScreen = ({ navigation }) => {
  const bookCategories = [
    { id: 1, title: '教科書', icon: 'school-outline', description: '各科系教科書訂購' },
    { id: 2, title: '參考書', icon: 'library-outline', description: '輔助學習參考資料' },
    { id: 3, title: '工具書', icon: 'construct-outline', description: '字典、手冊等工具書' },
    { id: 4, title: '期刊雜誌', icon: 'newspaper-outline', description: '學術期刊與雜誌' },
  ];

  const orderStatus = [
    { id: 1, title: '待確認', count: 3, color: '#FF9500' },
    { id: 2, title: '處理中', count: 2, color: '#007AFF' },
    { id: 3, title: '已完成', count: 8, color: '#34C759' },
  ];

  const renderCategoryItem = (category) => (
    <TouchableOpacity key={category.id} style={styles.categoryItem}>
      <View style={styles.categoryIcon}>
        <Ionicons name={category.icon} size={24} color="#364873" />
      </View>
      <View style={styles.categoryContent}>
        <Text style={styles.categoryTitle}>{category.title}</Text>
        <Text style={styles.categoryDescription}>{category.description}</Text>
      </View>
      <Ionicons name="chevron-forward" size={20} color="#ccc" />
    </TouchableOpacity>
  );

  const renderStatusItem = (status) => (
    <View key={status.id} style={styles.statusItem}>
      <View style={[styles.statusIndicator, { backgroundColor: status.color }]} />
      <Text style={styles.statusTitle}>{status.title}</Text>
      <Text style={styles.statusCount}>{status.count}</Text>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <Ionicons name="arrow-back" size={24} color="#333" style={styles.backIcon} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>訂書</Text>
        </View>
        <TouchableOpacity style={styles.cartButton}>
          <Ionicons name="cart-outline" size={24} color="#333" />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Quick Actions */}
        <View style={styles.quickActionsContainer}>
          <TouchableOpacity style={styles.quickActionButton}>
            <Ionicons name="add-circle-outline" size={24} color="#364873" />
            <Text style={styles.quickActionText}>新增訂單</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.quickActionButton}>
            <Ionicons name="list-outline" size={24} color="#364873" />
            <Text style={styles.quickActionText}>我的訂單</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.quickActionButton}>
            <Ionicons name="search-outline" size={24} color="#364873" />
            <Text style={styles.quickActionText}>書籍查詢</Text>
          </TouchableOpacity>
        </View>

        {/* Order Status */}
        <View style={styles.statusSection}>
          <Text style={styles.sectionTitle}>訂單狀態</Text>
          <View style={styles.statusContainer}>
            {orderStatus.map(renderStatusItem)}
          </View>
        </View>

        {/* Book Categories */}
        <View style={styles.categoriesSection}>
          <Text style={styles.sectionTitle}>書籍分類</Text>
          <View style={styles.categoriesList}>
            {bookCategories.map(renderCategoryItem)}
          </View>
        </View>

        {/* Info Card */}
        <View style={styles.infoSection}>
          <Text style={styles.sectionTitle}>訂書須知</Text>
          <View style={styles.infoCard}>
            <View style={styles.infoItem}>
              <Ionicons name="time-outline" size={20} color="#364873" />
              <Text style={styles.infoText}>訂書處理時間：3-5個工作天</Text>
            </View>
            <View style={styles.infoItem}>
              <Ionicons name="location-outline" size={20} color="#364873" />
              <Text style={styles.infoText}>取書地點：XXXXXX</Text>
            </View>
            <View style={styles.infoItem}>
              <Ionicons name="call-outline" size={20} color="#364873" />
              <Text style={styles.infoText}>聯絡電話：(02) 1234-5678</Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: 50,
    paddingBottom: 15,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  backIcon: {
    marginRight: 12,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  cartButton: {
    padding: 4,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
  },
  quickActionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 16,
  },
  quickActionButton: {
    flex: 1,
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    marginHorizontal: 4,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  quickActionText: {
    fontSize: 12,
    color: '#333',
    marginTop: 4,
    fontWeight: '500',
  },
  statusSection: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  statusContainer: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statusItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
  },
  statusIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 12,
  },
  statusTitle: {
    flex: 1,
    fontSize: 14,
    color: '#333',
  },
  statusCount: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
  },
  categoriesSection: {
    marginBottom: 24,
  },
  categoriesList: {
    backgroundColor: '#fff',
    borderRadius: 12,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  categoryItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  categoryIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f8f9fa',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  categoryContent: {
    flex: 1,
  },
  categoryTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  categoryDescription: {
    fontSize: 14,
    color: '#666',
  },
  infoSection: {
    marginBottom: 20,
  },
  infoCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
  },
  infoText: {
    marginLeft: 12,
    fontSize: 14,
    color: '#333',
  },
});

export default BookOrderScreen;
