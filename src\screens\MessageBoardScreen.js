import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  TextInput,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

const MessageBoardScreen = ({ navigation }) => {
  const [activeTab, setActiveTab] = useState('留言清單');
  const [messageText, setMessageText] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('本名');
  const [showCategoryDropdown, setShowCategoryDropdown] = useState(false);

  const tabs = ['留言清單', '我的留言', '發表留言'];
  const categories = ['本名', '匿名', '暱稱'];

  // 假資料：每則留言有 replies 陣列
  const messageData = [
    {
      id: 1,
      author: '王小明',
      studentId: 'ABCDEF123456',
      content: '這是一則留言內容...',
      timestamp: '2小時前',
      replies: [
        { id: 1, author: '老師', content: '感謝你的留言！', timestamp: '1小時前' },
        { id: 2, author: '同學A', content: '我也有同感！', timestamp: '30分鐘前' },
      ],
    },
    {
      id: 2,
      author: '李小華',
      studentId: 'ABCDEF123456',
      content: '另一則留言內容...',
      timestamp: '5小時前',
      replies: [
        { id: 1, author: '老師', content: '已收到，謝謝！', timestamp: '4小時前' },
      ],
    },
    {
      id: 3,
      author: '張小美',
      studentId: 'ABCDEF123456',
      content: '更多留言內容...',
      timestamp: '1天前',
      replies: [],
    },
  ];

  const myMessages = [
    {
      id: 1,
      author: '王小明（匿名：大明）',
      content: '我的留言內容...',
      timestamp: '1小時前',
    },
  ];

  const handleSubmitMessage = () => {
    if (!messageText.trim()) {
      Alert.alert('提醒', '請輸入留言內容');
      return;
    }
    Alert.alert('成功', '留言發表成功！');
    setMessageText('');
    setActiveTab('留言清單');
  };

  // 展開留言 id 狀態
  const [expandedIds, setExpandedIds] = useState([]);

  const toggleExpand = (id) => {
    setExpandedIds((prev) =>
      prev.includes(id) ? prev.filter((eid) => eid !== id) : [...prev, id]
    );
  };

  const renderMessageItem = (message, showAuthor = true) => {
    const expanded = expandedIds.includes(message.id);
    return (
      <TouchableOpacity
        key={message.id}
        style={styles.messageItem}
        activeOpacity={0.9}
        onPress={() => toggleExpand(message.id)}
      >
        <View style={styles.messageHeader}>
          <View style={styles.avatar}>
            <Ionicons name="person" size={20} color="#FF8C00" />
          </View>
          <View style={styles.messageInfo}>
            {showAuthor && (
              <>
                <Text style={styles.authorName}>{message.author}</Text>
                {message.studentId && (
                  <Text style={styles.studentId}>{message.studentId}</Text>
                )}
              </>
            )}
            <Text style={styles.messageContent}>{message.content}</Text>
            <View style={styles.messageFooter}>
              <Text style={styles.timestamp}>{message.timestamp}</Text>
              {message.replies && message.replies.length > 0 && (
                <Text style={styles.replies}>回覆 {message.replies.length}</Text>
              )}
            </View>
          </View>
        </View>
        {/* 展開回覆區塊 */}
        {expanded && message.replies && message.replies.length > 0 && (
          <View style={styles.replyCardContainer}>
            {message.replies.map((reply) => (
              <View key={reply.id} style={styles.replyCard}>
                <View style={styles.replyHeader}>
                  <Ionicons name="chatbubble-ellipses-outline" size={16} color="#364873" style={{marginRight: 6}} />
                  <Text style={styles.replyAuthor}>{reply.author}</Text>
                  <Text style={styles.replyTimestamp}>{reply.timestamp}</Text>
                </View>
                <Text style={styles.replyContent}>{reply.content}</Text>
              </View>
            ))}
          </View>
        )}
      </TouchableOpacity>
    );
  };
// ...existing code...

  const renderTabContent = () => {
    switch (activeTab) {
      case '留言清單':
        return (
          <ScrollView style={styles.tabContent} showsVerticalScrollIndicator={false}>
            {messageData.map((message) => renderMessageItem(message))}
          </ScrollView>
        );
      case '我的留言':
        return (
          <ScrollView style={styles.tabContent} showsVerticalScrollIndicator={false}>
            {myMessages.map((message) => renderMessageItem(message, false))}
          </ScrollView>
        );
      case '發表留言':
        return (
          <View style={styles.postMessageContainer}>
            <View style={styles.postHeader}>
              <Text style={styles.postAuthor}>發言人：</Text>
              <Text style={styles.postAuthorName}>王小明（匿名：大明）</Text>
            </View>

            <View style={styles.categoryContainer}>
              <Text style={styles.categoryLabel}>發表身分：</Text>
              <TouchableOpacity
                style={styles.categorySelector}
                onPress={() => setShowCategoryDropdown(!showCategoryDropdown)}
              >
                <Text style={styles.categoryText}>{selectedCategory}</Text>
                <Text style={styles.dropdownArrow}>▼</Text>
              </TouchableOpacity>
            </View>

            {showCategoryDropdown && (
              <View style={styles.categoryDropdown}>
                {categories.map((category) => (
                  <TouchableOpacity
                    key={category}
                    style={styles.categoryOption}
                    onPress={() => {
                      setSelectedCategory(category);
                      setShowCategoryDropdown(false);
                    }}
                  >
                    <Text style={styles.categoryOptionText}>{category}</Text>
                  </TouchableOpacity>
                ))}
              </View>
            )}

            <TextInput
              style={styles.messageInput}
              placeholder="請輸入你的留言... (限制100字)"
              multiline
              value={messageText}
              onChangeText={setMessageText}
              maxLength={100}
            />

            <TouchableOpacity
              style={styles.submitButton}
              onPress={handleSubmitMessage}
            >
              <Text style={styles.submitButtonText}>發布</Text>
            </TouchableOpacity>
          </View>
        );
      default:
        return null;
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Ionicons name="chevron-back" size={24} color="#333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>留言板</Text>
        <View style={styles.placeholder} />
      </View>

      {/* Tab Navigation */}
      <View style={styles.tabContainer}>
        {tabs.map((tab) => (
          <TouchableOpacity
            key={tab}
            style={[
              styles.tabButton,
              activeTab === tab && styles.activeTabButton,
            ]}
            onPress={() => setActiveTab(tab)}
          >
            <Text
              style={[
                styles.tabText,
                activeTab === tab && styles.activeTabText,
              ]}
            >
              {tab}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* Tab Content */}
      {renderTabContent()}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  replyCardContainer: {
    marginTop: 10,
  },
  replyCard: {
    backgroundColor: '#F5F7FB',
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
    borderLeftWidth: 4,
    borderLeftColor: '#364873',
  },
  replyHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  replyAuthor: {
    fontSize: 13,
    color: '#364873',
    fontWeight: 'bold',
    marginRight: 8,
  },
  replyTimestamp: {
    fontSize: 12,
    color: '#8E8E93',
  },
  replyContent: {
    fontSize: 14,
    color: '#333',
    marginLeft: 22,
  },
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 50,
    paddingBottom: 15,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
  },
  backButton: {
    padding: 5,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333333',
  },
  placeholder: {
    width: 24,
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
  },
  tabButton: {
    flex: 1,
    paddingVertical: 15,
    alignItems: 'center',
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  activeTabButton: {
    borderBottomColor: '#FF8C00',
  },
  tabText: {
    fontSize: 14,
    color: '#8E8E93',
    fontWeight: '500',
  },
  activeTabText: {
    color: '#FF8C00',
    fontWeight: 'bold',
  },
  tabContent: {
    flex: 1,
    padding: 20,
  },
  messageItem: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 15,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  messageHeader: {
    flexDirection: 'row',
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#E5E5EA',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  messageInfo: {
    flex: 1,
  },
  authorName: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 2,
  },
  studentId: {
    fontSize: 12,
    color: '#8E8E93',
    marginBottom: 8,
  },
  messageContent: {
    fontSize: 14,
    color: '#333333',
    lineHeight: 20,
    marginBottom: 10,
  },
  messageFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  timestamp: {
    fontSize: 12,
    color: '#8E8E93',
  },
  replies: {
    fontSize: 12,
    color: '#FF8C00',
  },
  postMessageContainer: {
    flex: 1,
    padding: 20,
  },
  postHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  postAuthor: {
    fontSize: 14,
    color: '#333333',
  },
  postAuthorName: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333333',
  },
  categoryContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  categoryLabel: {
    fontSize: 14,
    color: '#333333',
    marginRight: 10,
  },
  categorySelector: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 6,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderWidth: 1,
    borderColor: '#E5E5EA',
  },
  categoryText: {
    fontSize: 14,
    color: '#333333',
    marginRight: 8,
  },
  dropdownArrow: {
    fontSize: 12,
    color: '#8E8E93',
  },
  categoryDropdown: {
    backgroundColor: '#FFFFFF',
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#E5E5EA',
    marginBottom: 20,
  },
  categoryOption: {
    paddingHorizontal: 12,
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  categoryOptionText: {
    fontSize: 14,
    color: '#333333',
  },
  messageInput: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    padding: 15,
    height: 120,
    textAlignVertical: 'top',
    borderWidth: 1,
    borderColor: '#E5E5EA',
    fontSize: 14,
    marginBottom: 20,
  },
  submitButton: {
    backgroundColor: '#FF8C00',
    borderRadius: 8,
    paddingVertical: 15,
    alignItems: 'center',
  },
  submitButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
});

export default MessageBoardScreen;
