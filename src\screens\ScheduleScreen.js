import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  SafeAreaView,
  TouchableOpacity,
  Image,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

const ScheduleScreen = ({ navigation }) => {
  const [selectedDay, setSelectedDay] = useState('一');
  const weekDays = ['一', '二', '三', '四', '五', '六', '日'];
  const timeSlots = [
    '第一節',
    '第二節', 
    '第三節',
    '第四節',
    '第五節',
    '第六節',
    '第七節',
    '第八節',
    '第九節',
    '第A節',
    '第B節',
    '第C節',
    '第D節',
    '第E節',
  ];

  // Sample schedule data
  const scheduleData = {
    '一': {
      '第一節': { subject: '數學', classroom: 'A101' },
      '第二節': { subject: '數學', classroom: 'A101' },
      '第三節': { subject: '英文', classroom: 'B203' },
    },
    '二': {
      '第一節': { subject: '物理', classroom: 'C301' },
      '第二節': { subject: '物理', classroom: 'C301' },
      '第四節': { subject: '化學', classroom: 'D401' },
    },
    '三': {
      '第二節': { subject: '歷史', classroom: 'E501' },
      '第三節': { subject: '地理', classroom: 'F601' },
      '第五節': { subject: '體育', classroom: '體育館' },
    },
    '四': {
      '第一節': { subject: '國文', classroom: 'G701' },
      '第三節': { subject: '生物', classroom: 'H801' },
      '第四節': { subject: '生物', classroom: 'H801' },
    },
    '五': {
      '第二節': { subject: '音樂', classroom: '音樂教室' },
      '第三節': { subject: '美術', classroom: '美術教室' },
      '第六節': { subject: '電腦', classroom: '電腦教室' },
    },
  };

  const renderTimeSlot = (timeSlot) => {
    const currentDaySchedule = scheduleData[selectedDay] || {};
    const classInfo = currentDaySchedule[timeSlot];

    return (
      <View key={timeSlot} style={styles.timeSlotContainer}>
        <View style={styles.timeSlotLabel}>
          <Text style={styles.timeSlotText}>{timeSlot}</Text>
        </View>
        <View style={styles.classInfo}>
          {classInfo ? (
            <>
              <Text style={styles.subjectText}>{classInfo.subject}</Text>
              <Text style={styles.teacherText}>{classInfo.classroom}</Text>
              <Text style={styles.locationText}>授課教師</Text>
            </>
          ) : (
            <Text style={styles.noClassText}>無課程</Text>
          )}
        </View>
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <Image
            source={require('../../assets/images/lhu-logo.png')}
            style={styles.schoolIcon}
            resizeMode="contain"
          />
          <Text style={styles.greeting}>Hi,User!</Text>
        </View>
        <TouchableOpacity style={styles.notificationIcon}>
          <Ionicons name="notifications-outline" size={24} color="#fff" />
        </TouchableOpacity>
      </View>

      {/* Week Days Selector */}
      <View style={styles.weekSelector}>
        <Text style={styles.weekTitle}>星期</Text>
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.weekDaysContainer}>
          {weekDays.map((day) => (
            <TouchableOpacity
              key={day}
              style={[
                styles.dayButton,
                selectedDay === day && styles.selectedDayButton
              ]}
              onPress={() => setSelectedDay(day)}
            >
              <Text style={[
                styles.dayText,
                selectedDay === day && styles.selectedDayText
              ]}>
                {day}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      {/* Course Schedule Header */}
      <View style={styles.scheduleHeader}>
        <Text style={styles.scheduleHeaderText}>時間</Text>
        <Text style={styles.scheduleHeaderText}>課程</Text>
        <Text style={styles.scheduleHeaderText}>教室</Text>
        <Text style={styles.scheduleHeaderText}>授課教師</Text>
      </View>

      {/* Schedule List */}
      <ScrollView style={styles.scheduleList} showsVerticalScrollIndicator={false}>
        {timeSlots.map(renderTimeSlot)}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#364873',
    paddingHorizontal: 16,
    paddingTop: 50,
    paddingBottom: 15,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  schoolIcon: {
    width: 24,
    height: 24,
    marginRight: 8,
  },
  greeting: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
  },
  notificationIcon: {
    padding: 4,
  },
  weekSelector: {
    backgroundColor: '#fff',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  weekTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 12,
  },
  weekDaysContainer: {
    flexDirection: 'row',
  },
  dayButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 8,
    borderRadius: 20,
    backgroundColor: '#f0f0f0',
  },
  selectedDayButton: {
    backgroundColor: '#364873',
  },
  dayText: {
    fontSize: 14,
    color: '#666',
  },
  selectedDayText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  scheduleHeader: {
    backgroundColor: '#fff',
    flexDirection: 'row',
    paddingHorizontal: 5,
    paddingRight: 15,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  scheduleHeaderText: {
    flex: 1,
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
  },
  scheduleList: {
    flex: 1,
    backgroundColor: '#fff',
  },
  scrollView: {
    flex: 1,
  },
  scheduleContainer: {
    minWidth: 600,
    backgroundColor: '#FFFFFF',
    margin: 10,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  headerRow: {
    flexDirection: 'row',
    backgroundColor: '#4A90E2',
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,
  },
  timeSlotHeader: {
    width: 80,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    borderRightWidth: 1,
    borderRightColor: '#FFFFFF',
  },
  dayHeader: {
    width: 70,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    borderRightWidth: 1,
    borderRightColor: '#FFFFFF',
  },
  headerText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  scheduleGrid: {
    flex: 1,
  },
  scheduleRow: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
  },
  timeSlotCell: {
    width: 80,
    height: 60,
    justifyContent: 'center',
    alignItems: 'center',
    borderRightWidth: 1,
    borderRightColor: '#E5E5EA',
    backgroundColor: '#F8F9FA',
  },
  timeSlotText: {
    fontSize: 12,
    color: '#333333',
    fontWeight: '500',
  },
  scheduleCell: {
    width: 70,
    height: 60,
    borderRightWidth: 1,
    borderRightColor: '#E5E5EA',
    padding: 2,
  },
  timeSlotContainer: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    minHeight: 60,
  },
  timeSlotLabel: {
    width: 80,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f8f8f8',
    borderRightWidth: 1,
    borderRightColor: '#e0e0e0',
  },
  timeSlotText: {
    fontSize: 12,
    color: '#666',
    fontWeight: 'bold',
  },
  classInfo: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
  },
  subjectText: {
    flex: 1,
    fontSize: 14,
    color: '#333',
    textAlign: 'center',
  },
  teacherText: {
    flex: 1,
    fontSize: 14,
    color: '#333',
    textAlign: 'center',
  },
  locationText: {
    flex: 1,
    fontSize: 14,
    color: '#333',
    textAlign: 'center',
  },
  noClassText: {
    flex: 1,
    fontSize: 14,
    color: '#999',
    textAlign: 'center',
  },
  emptyCell: {
    flex: 1,
  },
});

export default ScheduleScreen;
