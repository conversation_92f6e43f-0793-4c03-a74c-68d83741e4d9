import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, SafeAreaView, Modal, FlatList } from 'react-native';

const roles = ['學生', '教職人員'];

const LoginScreen = ({ navigation }) => {
  const [selectedRole, setSelectedRole] = useState('學生');
  const [dropdownVisible, setDropdownVisible] = useState(false);

  const handleLogin = () => {
    // 這裡可根據 selectedRole 做不同導向或狀態儲存
    navigation.replace('MainTabs', { role: selectedRole });
  };

  return (
    <SafeAreaView style={styles.container}>
      <Text style={styles.title}>登入</Text>
      <View style={styles.dropdownWrapper}>
        <TouchableOpacity
          style={styles.dropdown}
          onPress={() => setDropdownVisible(true)}
        >
          <Text style={styles.dropdownText}>{selectedRole}</Text>
        </TouchableOpacity>
        <Modal
          visible={dropdownVisible}
          transparent
          animationType="fade"
          onRequestClose={() => setDropdownVisible(false)}
        >
          <TouchableOpacity style={styles.modalOverlay} onPress={() => setDropdownVisible(false)}>
            <View style={styles.dropdownList}>
              {roles.map((role) => (
                <TouchableOpacity
                  key={role}
                  style={styles.dropdownItem}
                  onPress={() => {
                    setSelectedRole(role);
                    setDropdownVisible(false);
                  }}
                >
                  <Text style={styles.dropdownItemText}>{role}</Text>
                </TouchableOpacity>
              ))}
            </View>
          </TouchableOpacity>
        </Modal>
      </View>
      <TouchableOpacity style={styles.loginButton} onPress={handleLogin}>
        <Text style={styles.loginButtonText}>登入</Text>
      </TouchableOpacity>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F5F5F5',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#364873',
    marginBottom: 40,
  },
  dropdownWrapper: {
    width: '80%',
    alignItems: 'center',
    marginBottom: 30,
  },
  dropdown: {
    width: '100%',
    backgroundColor: '#fff',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#364873',
    paddingVertical: 14,
    paddingHorizontal: 18,
    alignItems: 'center',
  },
  dropdownText: {
    fontSize: 18,
    color: '#364873',
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.2)',
  },
  dropdownList: {
    backgroundColor: '#fff',
    borderRadius: 8,
    width: 220,
    paddingVertical: 8,
    elevation: 8,
  },
  dropdownItem: {
    paddingVertical: 14,
    paddingHorizontal: 18,
    alignItems: 'center',
  },
  dropdownItemText: {
    fontSize: 18,
    color: '#364873',
  },
  loginButton: {
    backgroundColor: '#364873',
    borderRadius: 8,
    paddingVertical: 14,
    paddingHorizontal: 60,
    marginTop: 10,
  },
  loginButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
});

export default LoginScreen;
