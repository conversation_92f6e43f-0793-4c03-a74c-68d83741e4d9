import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Image,
  Dimensions,
  Linking,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

const HomeScreen = ({ navigation }) => {
  const [currentPage, setCurrentPage] = useState(0);
  const [currentBannerIndex, setCurrentBannerIndex] = useState(0);
  const [campusNews, setCampusNews] = useState([]);
  const [newsLoading, setNewsLoading] = useState(true);
  const screenWidth = Dimensions.get('window').width;
  const bannerScrollRef = useRef(null);

  // 輪播圖數據
  const bannerData = [
    {
      id: 1,
      image: require('../../assets/images/Carousel/Carousel_1.png'),
      title: '五專續招',
      description: '精彩校園生活等你參與'
    },
    {
      id: 2,
      image: require('../../assets/images/Carousel/Carousel_2.png'),
      title: '龍華榮譽',
      description: '私立技職由龍華科技大學再度拔得頭籌'
    },
    {
      id: 3,
      image: require('../../assets/images/Carousel/Carousel_3.png'),
      title: '龍華榮譽',
      description: '2025《遠見》企業最愛大學生調查 龍華科大入榜項數'
    },
    {
      id: 4,
      image: require('../../assets/images/Carousel/Carousel_4.png'),
      title: '龍華榮譽',
      description: '112學年度新生註冊率達91.62%，為學生人數5000人以上之綜合型私立科大第1名'
    },
    {
      id: 5,
      image: require('../../assets/images/Carousel/Carousel_5.png'),
      title: '龍華榮譽',
      description: '本校獲選教育部25所台灣大專院校人工智慧學程聯盟學校'
    },
    {
      id: 6,
      image: require('../../assets/images/Carousel/Carousel_6.png'),
      title: '龍華榮譽',
      description: '龍華科大獲高教深耕1.48億高額獎勵 蟬聯北台灣私立科大第一'
    }
  ];

  // 快速導航項目 - 第一頁（8個項目）
  const quickNavPage1 = [
    { id: 1, title: '投票', icon: 'checkmark-circle-outline', screen: 'Voting' },
    { id: 2, title: '二手市集', icon: 'storefront-outline', screen: 'SecondHand' },
    { id: 3, title: '票券', icon: 'ticket-outline', screen: 'ETickets' },
    { id: 4, title: '留言板', icon: 'chatbubbles-outline', screen: 'MessageBoard' },
    { id: 5, title: '訂書', icon: 'book-outline', screen: 'BookOrder' },
    { id: 6, title: '活動', icon: 'calendar-outline', screen: 'Activities' },
    { id: 7, title: '導覽', icon: 'map-outline', screen: 'CampusTour' },
    { id: 8, title: '美食地圖', icon: 'restaurant-outline', screen: 'FoodMap' },
  ];

  // 快速導航項目 - 第二頁（只保留報修功能）
  const quickNavPage2 = [
    { id: 9, title: '報修', icon: 'construct-outline', screen: 'Repair' },
  ];

  const quickNavPages = [quickNavPage1, quickNavPage2];

  // 自動輪播效果
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentBannerIndex((prevIndex) => {
        const nextIndex = (prevIndex + 1) % bannerData.length;
        if (bannerScrollRef.current) {
          bannerScrollRef.current.scrollTo({
            x: nextIndex * screenWidth,
            animated: true,
          });
        }
        return nextIndex;
      });
    }, 3000); // 每3秒切換一次

    return () => clearInterval(timer);
  }, [screenWidth, bannerData.length]);

  // 載入校園新聞
  useEffect(() => {
    fetchCampusNews();
  }, []);

  const handleNavigation = (screen) => {
    navigation.navigate(screen);
  };

  // 抓取校園新聞
  const fetchCampusNews = async () => {
    try {
      setNewsLoading(true);
      const response = await fetch('https://www.lhu.edu.tw/p/403-1000-11-1.php?Lang=zh-tw');
      const html = await response.text();

      // 解析 HTML 獲取新聞資料
      const newsData = parseNewsFromHTML(html);
      setCampusNews(newsData.slice(0, 8)); // 只取前8則新聞
    } catch (error) {
      console.error('抓取校園新聞失敗:', error);
      // 設置一些預設新聞作為備用
      setCampusNews([
        {
          date: '2025-07-19',
          title: '龍華科技大學亮點新聞',
          unit: '秘書室',
          link: 'https://www.lhu.edu.tw'
        }
      ]);
    } finally {
      setNewsLoading(false);
    }
  };

  // 解析 HTML 中的新聞資料
  const parseNewsFromHTML = (html) => {
    const newsArray = [];
    try {
      // 根據實際的 HTML 結構解析新聞
      // 尋找新聞項目的模式：<div class="d-item d-title col-sm-12">
      const newsItemPattern = /<div class="d-item d-title col-sm-12">[\s\S]*?<\/div>[\s\S]*?<\/div>[\s\S]*?<\/div>/g;
      let newsItemMatch;

      while ((newsItemMatch = newsItemPattern.exec(html)) !== null && newsArray.length < 10) {
        const newsItemHtml = newsItemMatch[0];

        // 從每個新聞項目中提取日期
        const dateMatch = newsItemHtml.match(/<i class="mdate before">(\d{4}-\d{2}-\d{2})<\/i>/);

        // 從每個新聞項目中提取標題和連結
        const linkMatch = newsItemHtml.match(/<a href="([^"]*)"[^>]*title="[^"]*">([^<]+)<\/a>/);

        if (dateMatch && linkMatch) {
          const date = dateMatch[1];
          const link = linkMatch[1];
          const title = linkMatch[2].trim();

          // 清理標題中的HTML實體和多餘空格
          const cleanTitle = title
            .replace(/&nbsp;/g, ' ')
            .replace(/&amp;/g, '&')
            .replace(/&lt;/g, '<')
            .replace(/&gt;/g, '>')
            .replace(/&quot;/g, '"')
            .replace(/&#39;/g, "'")
            .replace(/\s+/g, ' ')
            .trim();

          // 從標題中提取媒體來源作為單位
          const mediaMatch = cleanTitle.match(/【([^】]+)】/);
          const unit = mediaMatch ? mediaMatch[1] : '亮點新聞';

          // 移除媒體標籤後的標題
          const titleWithoutMedia = cleanTitle.replace(/【[^】]*】\s*/, '');

          if (titleWithoutMedia && date) {
            newsArray.push({
              date: date,
              title: titleWithoutMedia,
              link: link.startsWith('http') ? link : `https://www.lhu.edu.tw${link}`,
              unit: unit
            });
          }
        }
      }

      // 如果上面的方法沒有找到新聞，嘗試更簡單的解析方法
      if (newsArray.length === 0) {
        console.log('使用備用解析方法');

        // 尋找日期模式
        const datePattern = /<i class="mdate before">(\d{4}-\d{2}-\d{2})<\/i>/g;
        const dates = [];
        let dateMatch;
        while ((dateMatch = datePattern.exec(html)) !== null) {
          dates.push(dateMatch[1]);
        }

        // 尋找連結和標題模式
        const linkPattern = /<a href="([^"]*)"[^>]*>([^<]+)<\/a>/g;
        const links = [];
        let linkMatch;
        while ((linkMatch = linkPattern.exec(html)) !== null) {
          // 過濾掉非新聞連結
          if (linkMatch[1].includes('money.udn.com') ||
              linkMatch[1].includes('chinatimes.com') ||
              linkMatch[1].includes('lhu.edu.tw')) {
            links.push({
              link: linkMatch[1],
              title: linkMatch[2].trim()
            });
          }
        }

        // 組合日期和標題
        const minLength = Math.min(dates.length, links.length, 10);
        for (let i = 0; i < minLength; i++) {
          const title = links[i].title;
          const mediaMatch = title.match(/【([^】]+)】/);
          const unit = mediaMatch ? mediaMatch[1] : '亮點新聞';
          const cleanTitle = title.replace(/【[^】]*】\s*/, '');

          if (cleanTitle) {
            newsArray.push({
              date: dates[i],
              title: cleanTitle,
              link: links[i].link.startsWith('http') ? links[i].link : `https://www.lhu.edu.tw${links[i].link}`,
              unit: unit
            });
          }
        }
      }

      console.log(`成功解析 ${newsArray.length} 則新聞`);
    } catch (error) {
      console.error('解析新聞 HTML 失敗:', error);
    }

    return newsArray;
  };

  // 處理系統連結
  const handleSystemLink = async (url, systemName) => {
    try {
      const supported = await Linking.canOpenURL(url);
      if (supported) {
        await Linking.openURL(url);
      } else {
        Alert.alert('錯誤', `無法開啟${systemName}，請檢查網路連線或稍後再試。`);
      }
    } catch (error) {
      Alert.alert('錯誤', `開啟${systemName}時發生錯誤，請稍後再試。`);
    }
  };

  // 處理新聞點擊
  const handleNewsClick = async (newsItem) => {
    try {
      const supported = await Linking.canOpenURL(newsItem.link);
      if (supported) {
        await Linking.openURL(newsItem.link);
      } else {
        Alert.alert('錯誤', '無法開啟新聞連結，請稍後再試。');
      }
    } catch (error) {
      Alert.alert('錯誤', '開啟新聞時發生錯誤，請稍後再試。');
    }
  };

  const renderQuickNavGrid = () => {
    return (
      <View style={styles.quickNavCard}>
        <ScrollView
          horizontal
          pagingEnabled
          showsHorizontalScrollIndicator={false}
          onMomentumScrollEnd={(event) => {
            const page = Math.round(event.nativeEvent.contentOffset.x / (screenWidth - 32));
            setCurrentPage(page);
          }}
          style={styles.quickNavScrollView}
        >
          {quickNavPages.map((pageItems, pageIndex) => (
            <View key={pageIndex} style={[styles.quickNavPage, { width: screenWidth - 32 }]}>
              <View style={styles.quickNavGrid}>
                {pageItems.map((item) => (
                  <TouchableOpacity
                    key={item.id}
                    style={styles.quickNavItem}
                    onPress={() => handleNavigation(item.screen)}
                  >
                    <View style={styles.iconContainer}>
                      <Ionicons name={item.icon} size={24} color="#666" />
                    </View>
                    <Text style={styles.quickNavText}>{item.title}</Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          ))}
        </ScrollView>

        {/* 頁面指示器 */}
        <View style={styles.pageIndicator}>
          {quickNavPages.map((_, index) => (
            <View
              key={index}
              style={[
                styles.dot,
                { backgroundColor: currentPage === index ? '#FF8C00' : '#E5E5EA' }
              ]}
            />
          ))}
        </View>
      </View>
    );
  };

  const renderBanner = () => (
    <View style={styles.bannerContainer}>
      <ScrollView
        ref={bannerScrollRef}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onMomentumScrollEnd={(event) => {
          const index = Math.round(event.nativeEvent.contentOffset.x / screenWidth);
          setCurrentBannerIndex(index);
        }}
        style={styles.bannerScrollView}
      >
        {bannerData.map((item, index) => (
          <TouchableOpacity
            key={item.id}
            style={[styles.bannerSlide, { width: screenWidth - 32 }]}
            activeOpacity={0.9}
          >
            <Image
              source={item.image}
              style={styles.bannerImage}
              resizeMode="cover"
            />
            <View style={styles.bannerOverlay}>
              <Text style={styles.bannerTitle}>{item.title}</Text>
              <Text style={styles.bannerDescription}>{item.description}</Text>
            </View>
          </TouchableOpacity>
        ))}
      </ScrollView>

      {/* 指示器 */}
      <View style={styles.bannerIndicator}>
        {bannerData.map((_, index) => (
          <View
            key={index}
            style={[
              styles.bannerDot,
              { backgroundColor: currentBannerIndex === index ? '#FF8C00' : '#E5E5EA' }
            ]}
          />
        ))}
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <Image
            source={require('../../assets/images/lhu-logo.png')}
            style={styles.schoolIcon}
            resizeMode="contain"
          />
          <Text style={styles.greeting}>Hi,User!</Text>
        </View>
        <TouchableOpacity style={styles.notificationIcon}>
          <Ionicons name="notifications-outline" size={24} color="#fff" />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Banner Section */}
        {renderBanner()}

        {/* System Links */}
        <View style={styles.systemLinksContainer}>
          <TouchableOpacity
            style={styles.systemLinkButton}
            onPress={() => handleSystemLink('https://eportal.lhu.edu.tw/index.do', '校務系統')}
          >
            <Ionicons name="school-outline" size={20} color="#364873" style={styles.systemLinkIcon} />
            <Text style={styles.systemLinkText}>校務系統連結</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.systemLinkButton}
            onPress={() => handleSystemLink('https://sys.lhu.edu.tw/CourseAp/SGLogin.aspx', '選課系統')}
          >
            <Ionicons name="library-outline" size={20} color="#364873" style={styles.systemLinkIcon} />
            <Text style={styles.systemLinkText}>選課系統連結</Text>
          </TouchableOpacity>
        </View>

        {/* Quick Navigation Section */}
        <View style={styles.quickNavSection}>
          <Text style={styles.sectionTitle}>快速導航列</Text>
          {renderQuickNavGrid()}
        </View>

        {/* Campus News Section */}
        <View style={styles.campusNewsSection}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>亮點新聞</Text>
            <TouchableOpacity
              onPress={() => handleSystemLink('https://www.lhu.edu.tw/p/403-1000-11-1.php?Lang=zh-tw', '亮點新聞')}
            >
              <Text style={styles.moreNewsText}>更多新聞</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.newsContainer}>
            {newsLoading ? (
              <View style={styles.loadingContainer}>
                <Text style={styles.loadingText}>載入中...</Text>
              </View>
            ) : campusNews.length > 0 ? (
              campusNews.map((news, index) => (
                <TouchableOpacity
                  key={index}
                  style={styles.newsItem}
                  onPress={() => handleNewsClick(news)}
                  activeOpacity={0.7}
                >
                  <View style={styles.newsContent}>
                    <Text style={styles.newsTitle} numberOfLines={2}>
                      {news.title}
                    </Text>
                    <View style={styles.newsInfo}>
                      <Text style={styles.newsDate}>{news.date}</Text>
                      <Text style={styles.newsUnit}>{news.unit}</Text>
                    </View>
                  </View>
                  <Ionicons name="chevron-forward" size={16} color="#999" />
                </TouchableOpacity>
              ))
            ) : (
              <View style={styles.noNewsContainer}>
                <Text style={styles.noNewsText}>暫無新聞資料</Text>
              </View>
            )}
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#364873',
    paddingHorizontal: 16,
    paddingTop: 50,
    paddingBottom: 15,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  schoolIcon: {
    width: 24,
    height: 24,
    marginRight: 8,
  },
  greeting: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
  },
  notificationIcon: {
    padding: 10, 
  },
  scrollView: {
    flex: 1,
  },
  bannerContainer: {
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 8,
  },
  bannerScrollView: {
    borderRadius: 12,
    overflow: 'hidden',
  },
  bannerSlide: {
    height: 180,
    marginHorizontal: 0,
    borderRadius: 12,
    overflow: 'hidden',
    position: 'relative',
  },
  bannerImage: {
    width: '100%',
    height: '100%',
  },
  bannerOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
    padding: 16,
  },
  bannerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 4,
  },
  bannerDescription: {
    fontSize: 14,
    color: '#fff',
    opacity: 0.9,
  },
  bannerIndicator: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 12,
    gap: 8,
  },
  bannerDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#ccc',
  },
  activeDot: {
    backgroundColor: '#364873',
  },
  systemLinksContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 12,
  },
  systemLinkButton: {
    flex: 1,
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    flexDirection: 'row',
    justifyContent: 'center',
  },
  systemLinkIcon: {
    marginRight: 8,
  },
  systemLinkText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
  },
  quickNavSection: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  quickNavCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    marginTop: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  quickNavScrollView: {
    paddingHorizontal: 16,
    paddingTop: 16,
  },
  quickNavPage: {
    paddingHorizontal: 0,
  },
  quickNavGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-around',
    paddingHorizontal: 16,
  },
  quickNavItem: {
    width: '22%', 
    height: 80,
    paddingRight: 35,
    justifyContent: 'flex-start',
    alignItems: 'center',
    marginBottom: 16,
    backgroundColor: 'transparent',
    paddingTop: 20,
  },
  iconContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  quickNavText: {
    fontSize: 11,
    color: '#333',
    textAlign: 'center',
    fontWeight: '500',
  },
  pageIndicator: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 12,
    gap: 8,
  },

  campusNewsSection: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    paddingBottom: 20,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  moreNewsText: {
    fontSize: 14,
    color: '#FF8C00',
    fontWeight: '500',
  },
  newsContainer: {
    backgroundColor: '#fff',
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 3,
  },
  loadingContainer: {
    padding: 24,
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: 120,
  },
  loadingText: {
    fontSize: 14,
    color: '#999',
  },
  newsItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  newsContent: {
    flex: 1,
    marginRight: 12,
  },
  newsTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
    lineHeight: 20,
    marginBottom: 6,
  },
  newsInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  newsDate: {
    fontSize: 12,
    color: '#666',
  },
  newsUnit: {
    fontSize: 12,
    color: '#999',
    backgroundColor: '#f5f5f5',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 10,
  },
  noNewsContainer: {
    padding: 24,
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: 120,
  },
  noNewsText: {
    fontSize: 14,
    color: '#999',
  },
  newsPlaceholder: {
    fontSize: 14,
    color: '#999',
  },
});

export default HomeScreen;
