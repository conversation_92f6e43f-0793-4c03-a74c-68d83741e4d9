import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createStackNavigator } from '@react-navigation/stack';
import { StatusBar } from 'expo-status-bar';
import { Ionicons } from '@expo/vector-icons';

// Import the main screens
import HomeScreen from './src/screens/HomeScreen';
import ScheduleScreen from './src/screens/ScheduleScreen';
import BulletinScreen from './src/screens/BulletinScreen';
import ProfileScreen from './src/screens/ProfileScreen';

// Import feature screens
import VotingScreen from './src/screens/VotingScreen';
import ETicketsScreen from './src/screens/ETicketsScreen';
import MessageBoardScreen from './src/screens/MessageBoardScreen';
import ActivitiesScreen from './src/screens/ActivitiesScreen';
import FoodMapScreen from './src/screens/FoodMapScreen';
import CampusTourScreen from './src/screens/CampusTourScreen';
import RepairScreen from './src/screens/RepairScreen';
import BulletinDetailScreen from './src/screens/BulletinDetailScreen';

import LibraryScreen from './src/screens/LibraryScreen';
import SecondHandScreen from './src/screens/SecondHandScreen';
import BookOrderScreen from './src/screens/BookOrderScreen';
import ActivityDetailScreen from './src/screens/ActivityDetailScreen';
import LoginScreen from './src/screens/LoginScreen';


const Tab = createBottomTabNavigator();
const Stack = createStackNavigator();



// Tab Navigator Component
function TabNavigator() {
  return (
    <Tab.Navigator
      screenOptions={{
        tabBarActiveTintColor: '#FF6B35',
        tabBarInactiveTintColor: '#8E8E93',
        tabBarStyle: {
          backgroundColor: '#FFFFFF',
          borderTopWidth: 1,
          borderTopColor: '#E5E5EA',
          height: 80,
          paddingBottom: 8,
          paddingTop: 8,
        },
        headerShown: false, // Hide tab navigator headers since we'll use stack headers
      }}
    >
      <Tab.Screen
        name="Home"
        component={HomeScreen}
        options={{
          title: '首頁',
          tabBarLabel: '首頁',
          tabBarIcon: ({ color, size }) => (
            <Ionicons name="home-outline" size={size} color={color} />
          ),
        }}
      />
      <Tab.Screen
        name="Schedule"
        component={ScheduleScreen}
        options={{
          title: '課表',
          tabBarLabel: '課表',
          tabBarIcon: ({ color, size }) => (
            <Ionicons name="calendar-outline" size={size} color={color} />
          ),
        }}
      />
      <Tab.Screen
        name="Bulletin"
        component={BulletinScreen}
        options={{
          title: '佈告欄',
          tabBarLabel: '佈告欄',
          tabBarIcon: ({ color, size }) => (
            <Ionicons name="newspaper-outline" size={size} color={color} />
          ),
        }}
      />
      <Tab.Screen
        name="Profile"
        component={ProfileScreen}
        options={{
          title: '個人中心',
          tabBarLabel: '個人中心',
          tabBarIcon: ({ color, size }) => (
            <Ionicons name="person-outline" size={size} color={color} />
          ),
        }}
      />
    </Tab.Navigator>
  );
}

export default function App() {
  return (
    <NavigationContainer>
      <StatusBar style="auto" />
      <Stack.Navigator
        initialRouteName="Login"
        screenOptions={{
          headerStyle: {
            backgroundColor: '#364873',
          },
          headerTintColor: '#FFFFFF',
          headerTitleStyle: {
            fontWeight: 'bold',
          },
        }}
      >
        <Stack.Screen
          name="MainTabs"
          component={TabNavigator}
          options={{ headerShown: false }}
        />
        <Stack.Screen
          name="Voting"
          component={VotingScreen}
          options={{ headerShown: false }}
        />
        <Stack.Screen
          name="ETickets"
          component={ETicketsScreen}
          options={{ headerShown: false }}
        />
        <Stack.Screen
          name="MessageBoard"
          component={MessageBoardScreen}
          options={{ headerShown: false }}
        />
        <Stack.Screen
          name="Activities"
          component={ActivitiesScreen}
          options={{ headerShown: false }}
        />
        <Stack.Screen
          name="FoodMap"
          component={FoodMapScreen}
          options={{ headerShown: false }}
        />
        <Stack.Screen
          name="CampusTour"
          component={CampusTourScreen}
          options={{ headerShown: false }}
        />
        <Stack.Screen
          name="Repair"
          component={RepairScreen}
          options={{ headerShown: false }}
        />
        <Stack.Screen
          name="BulletinDetail"
          component={BulletinDetailScreen}
          options={{ headerShown: false }}
        />

        <Stack.Screen
          name="Library"
          component={LibraryScreen}
          options={{ headerShown: false }}
        />
        <Stack.Screen
          name="SecondHand"
          component={SecondHandScreen}
          options={{ headerShown: false }}
        />
        <Stack.Screen
          name="BookOrder"
          component={BookOrderScreen}
          options={{ headerShown: false }}
        />
        <Stack.Screen
          name="ActivityDetail"
          component={ActivityDetailScreen}
          options={{ headerShown: false }}
        />
        <Stack.Screen
          name="Login"
          component={LoginScreen}
          options={{ headerShown: false }}
        />

      </Stack.Navigator>
    </NavigationContainer>
  );
}


