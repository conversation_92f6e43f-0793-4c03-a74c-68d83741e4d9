import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Modal,
  Image,
} from 'react-native';

const ETicketsScreen = ({ navigation }) => {
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedTicket, setSelectedTicket] = useState(null);

  const ticketData = [
    {
      id: 1,
      name: 'Item Name 1',
      status: '可使用',
      validPeriod: '2025/06/25~2112/09/03',
      actions: ['兌換', '轉送'],
      image: 'ticket_image_1',
    },
    {
      id: 2,
      name: 'Item Name 2',
      status: '已兌換',
      validPeriod: '2025/06/25~2112/09/03',
      actions: ['查看詳情'],
      image: 'ticket_image_2',
    },
    {
      id: 3,
      name: 'Item Name 3',
      status: '已過期',
      validPeriod: '2024/06/25~2024/09/03',
      actions: ['查看詳情'],
      image: 'ticket_image_3',
    },
  ];

  const handleTicketPress = (ticket) => {
    setSelectedTicket(ticket);
    setModalVisible(true);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case '可使用':
        return '#4CAF50';
      case '已兌換':
        return '#FF9800';
      case '已過期':
        return '#F44336';
      default:
        return '#8E8E93';
    }
  };

  const getActionButtonStyle = (action) => {
    switch (action) {
      case '兌換':
        return { backgroundColor: '#4CAF50' };
      case '轉送':
        return { backgroundColor: '#2196F3' };
      case '查看詳情':
        return { backgroundColor: '#FF9800' };
      default:
        return { backgroundColor: '#8E8E93' };
    }
  };

  const renderTicketItem = (ticket) => (
    <TouchableOpacity
      key={ticket.id}
      style={styles.ticketItem}
      onPress={() => handleTicketPress(ticket)}
    >
      <View style={styles.ticketHeader}>
        <Text style={styles.ticketName}>{ticket.name}</Text>
        <Text style={[styles.ticketStatus, { color: getStatusColor(ticket.status) }]}>
          {ticket.status}
        </Text>
      </View>
      <Text style={styles.ticketPeriod}>{ticket.validPeriod}</Text>
      <View style={styles.ticketActions}>
        {ticket.actions.map((action, index) => (
          <TouchableOpacity
            key={index}
            style={[styles.actionButton, getActionButtonStyle(action)]}
          >
            <Text style={styles.actionButtonText}>{action}</Text>
          </TouchableOpacity>
        ))}
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Text style={styles.backButton}>‹</Text>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>我的優惠券</Text>
        <View style={styles.placeholder} />
      </View>

      {/* Tickets List */}
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <View style={styles.ticketsList}>
          {ticketData.map(renderTicketItem)}
        </View>
      </ScrollView>

      {/* Ticket Detail Modal */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>
                {selectedTicket?.name || 'Item Name 1'}
              </Text>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => setModalVisible(false)}
              >
                <Text style={styles.closeButtonText}>✕</Text>
              </TouchableOpacity>
            </View>

            <Text style={styles.modalPeriod}>
              {selectedTicket?.validPeriod || '2025/06/25~2112/09/03'}
            </Text>

            <View style={styles.ticketImageContainer}>
              <View style={styles.ticketImagePlaceholder}>
                <Text style={styles.ticketImageText}>Item Image</Text>
              </View>
            </View>

            <TouchableOpacity style={styles.useTicketButton}>
              <Text style={styles.useTicketButtonText}>立即使用</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 50,
    paddingBottom: 15,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
  },
  backButton: {
    fontSize: 24,
    color: '#4A90E2',
    fontWeight: 'bold',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333333',
  },
  placeholder: {
    width: 24,
  },
  scrollView: {
    flex: 1,
  },
  ticketsList: {
    padding: 20,
  },
  ticketItem: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 20,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  ticketHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  ticketName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333333',
    flex: 1,
  },
  ticketStatus: {
    fontSize: 14,
    fontWeight: '500',
  },
  ticketPeriod: {
    fontSize: 12,
    color: '#8E8E93',
    marginBottom: 15,
  },
  ticketActions: {
    flexDirection: 'row',
    gap: 10,
  },
  actionButton: {
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 6,
  },
  actionButtonText: {
    fontSize: 12,
    color: '#FFFFFF',
    fontWeight: '500',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 20,
    width: '85%',
    maxHeight: '70%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333333',
    flex: 1,
  },
  closeButton: {
    padding: 5,
  },
  closeButtonText: {
    fontSize: 18,
    color: '#8E8E93',
  },
  modalPeriod: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 20,
  },
  ticketImageContainer: {
    alignItems: 'center',
    marginBottom: 30,
  },
  ticketImagePlaceholder: {
    width: 200,
    height: 120,
    backgroundColor: '#000000',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  ticketImageText: {
    fontSize: 16,
    color: '#FFFFFF',
    fontWeight: '500',
  },
  useTicketButton: {
    backgroundColor: '#FF6B35',
    borderRadius: 8,
    paddingVertical: 15,
    alignItems: 'center',
  },
  useTicketButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
});

export default ETicketsScreen;
