# 校園APP - Campus App

這是一個使用React Native和Expo開發的校園應用程式，採用前後端分離的架構設計。

## 功能特色

### 主要頁面
- **首頁**: 包含快速導航功能，支持兩頁切換
- **課表**: 顯示週課程安排的時間表
- **布告欄**: 包含推薦資訊、系所公告、校園公告三個子頁面
- **個人中心**: 用戶信息、QR碼顯示和功能設置

### 快速導航功能
- **投票**: 支持投票功能，包含模態框互動
- **電子票券**: 票券管理和使用，包含模態框顯示
- **留言板**: 三個標籤頁（留言清單、我的留言、發表留言）
- **活動**: 校園活動瀏覽和篩選
- **美食地圖**: 校園美食位置和推薦
- **校園導覽**: 校園地圖和樓層平面圖
- **報修**: 設備報修申請系統

## 技術架構

- **框架**: React Native + Expo
- **導航**: React Navigation (Stack + Bottom Tabs)
- **UI組件**: React Native內建組件
- **狀態管理**: React Hooks (useState)

## 安裝和運行

### 前置要求
- Node.js (版本 14 或更高)
- npm 或 yarn
- Expo CLI
- Android Studio

### 安裝步驟

1. 進入項目目錄
```bash
cd CampusApp
```

2. 安裝依賴
```bash
npm install
```

3. 啟動開發服務器
```bash
npm start
```

4. 使用Expo Go應用掃描QR碼在手機上預覽，或使用模擬器

## 項目結構

```
CampusApp/
├── App.js                 # 主應用入口，包含導航配置
├── src/
│   └── screens/           # 所有頁面組件
│       ├── HomeScreen.js          # 首頁
│       ├── ScheduleScreen.js      # 課表頁面
│       ├── BulletinScreen.js      # 布告欄頁面
│       ├── ProfileScreen.js       # 個人中心頁面
│       ├── VotingScreen.js        # 投票頁面
│       ├── ETicketsScreen.js      # 電子票券頁面
│       ├── MessageBoardScreen.js  # 留言板頁面
│       ├── ActivitiesScreen.js    # 活動頁面
│       ├── FoodMapScreen.js       # 美食地圖頁面
│       ├── CampusTourScreen.js    # 校園導覽頁面
│       ├── RepairScreen.js        # 報修頁面
│       └── BulletinDetailScreen.js # 布告欄詳情頁面
├── assets/                # 靜態資源
└── package.json          # 項目配置和依賴
```

## 設計特點

- **響應式設計**: 適配不同屏幕尺寸
- **模態框互動**: 投票和電子票券功能
- **標籤頁導航**: 布告欄和留言板的子頁面切換
- **搜索和篩選**: 多個頁面支持搜索功能
- **現代UI**: 使用陰影、圓角和現代色彩搭配

## 開發說明

這個應用目前是前端原型，所有數據都是模擬數據。在實際部署時需要：

1. 連接後端API
2. 實現用戶認證
3. 添加真實的地圖功能
4. 實現推送通知
5. 添加圖片上傳功能

## 支持的功能

✅ 底部導航欄
✅ 頁面間導航
✅ 模態框互動
✅ 表單輸入
✅ 列表顯示
✅ 搜索界面
✅ 標籤頁切換
✅ 響應式佈局

## 後續開發

- [ ] 連接後端API
- [ ] 用戶認證系統
- [ ] 推送通知
- [ ] 離線功能
- [ ] 圖片上傳
- [ ] 地圖集成
- [ ] 性能優化

## 聯繫方式

如有問題或建議，請聯繫開發團隊。
