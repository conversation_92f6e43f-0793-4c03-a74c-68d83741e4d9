import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Modal,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

const VotingScreen = ({ navigation }) => {
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedVote, setSelectedVote] = useState(null);
  const [selectedOption, setSelectedOption] = useState('');

  const votingData = [
    {
      id: 1,
      title: 'XXXX投票',
      code: '編號123456789',
      deadline: '截止時間',
      participants: '已有X人參與',
      options: ['是', '否', '無意見'],
    },
    {
      id: 2,
      title: 'XXXX投票',
      code: '編號987654321',
      deadline: '截止時間',
      participants: '已有X人參與',
      options: ['同意', '不同意', '棄權'],
    },
  ];

  const handleVotePress = (vote) => {
    setSelectedVote(vote);
    setSelectedOption('');
    setModalVisible(true);
  };

  const handleSubmitVote = () => {
    if (!selectedOption) {
      Alert.alert('提醒', '請選擇一個選項');
      return;
    }
    Alert.alert('投票成功', `您選擇了：${selectedOption}`);
    setModalVisible(false);
    setSelectedOption('');
  };

  const renderVoteItem = (vote) => (
    <TouchableOpacity
      key={vote.id}
      style={styles.voteItem}
      onPress={() => handleVotePress(vote)}
    >
      <Text style={styles.voteTitle}>{vote.title}</Text>
      <Text style={styles.voteCode}>{vote.code}</Text>
      <View style={styles.voteFooter}>
        <Text style={styles.voteDeadline}>{vote.deadline}</Text>
        <Text style={styles.voteParticipants}>{vote.participants}</Text>
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Ionicons name="chevron-back" size={24} color="#333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>投票</Text>
        <View style={styles.placeholder} />
      </View>

      {/* Voting List */}
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <View style={styles.votingList}>
          {votingData.map(renderVoteItem)}
        </View>
      </ScrollView>

      {/* Voting Modal */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <TouchableOpacity onPress={() => setModalVisible(false)} style={styles.modalBackButton}>
                <Ionicons name="chevron-back" size={24} color="#333" />
              </TouchableOpacity>
              <Text style={styles.modalTitle}>
                {selectedVote?.title || 'XXXX投票'}
              </Text>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => setModalVisible(false)}
              >
                <Ionicons name="close" size={24} color="#333" />
              </TouchableOpacity>
            </View>

            <Text style={styles.modalCode}>
              {selectedVote?.code || '編號123456789'}
            </Text>

            <View style={styles.optionsContainer}>
              {selectedVote?.options.map((option, index) => (
                <TouchableOpacity
                  key={index}
                  style={[
                    styles.optionButton,
                    selectedOption === option && styles.selectedOption,
                  ]}
                  onPress={() => setSelectedOption(option)}
                >
                  <View style={styles.radioButton}>
                    {selectedOption === option && (
                      <View style={styles.radioButtonSelected} />
                    )}
                  </View>
                  <Text style={styles.optionText}>{option}</Text>
                </TouchableOpacity>
              ))}
            </View>

            <TouchableOpacity
              style={styles.submitButton}
              onPress={handleSubmitVote}
            >
              <Text style={styles.submitButtonText}>投票</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 50,
    paddingBottom: 15,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
  },
  backButton: {
    padding: 5,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333333',
  },
  placeholder: {
    width: 34,
  },
  scrollView: {
    flex: 1,
  },
  votingList: {
    padding: 20,
  },
  voteItem: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 20,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  voteTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 8,
  },
  voteCode: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 12,
  },
  voteFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  voteDeadline: {
    fontSize: 12,
    color: '#8E8E93',
  },
  voteParticipants: {
    fontSize: 12,
    color: '#4A90E2',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 20,
    width: '85%',
    maxHeight: '70%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
    paddingBottom: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
  },
  modalBackButton: {
    padding: 5,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333333',
    flex: 1,
    textAlign: 'center',
  },
  closeButton: {
    padding: 5,
  },
  modalCode: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 20,
  },
  optionsContainer: {
    marginBottom: 30,
  },
  optionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 15,
    marginBottom: 10,
    borderRadius: 8,
    backgroundColor: '#F8F9FA',
  },
  selectedOption: {
    backgroundColor: '#E3F2FD',
  },
  radioButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#4A90E2',
    marginRight: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  radioButtonSelected: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: '#4A90E2',
  },
  optionText: {
    fontSize: 16,
    color: '#333333',
  },
  submitButton: {
    backgroundColor: '#FF8C00',
    borderRadius: 8,
    paddingVertical: 15,
    alignItems: 'center',
  },
  submitButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
});

export default VotingScreen;
