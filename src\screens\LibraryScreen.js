import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

const LibraryScreen = ({ navigation }) => {
  const libraryServices = [
    { id: 1, title: '圖書查詢', icon: 'search-outline', description: '查詢館藏圖書資料' },
    { id: 2, title: '借閱記錄', icon: 'list-outline', description: '查看個人借閱歷史' },
    { id: 3, title: '預約圖書', icon: 'bookmark-outline', description: '預約想要借閱的圖書' },
    { id: 4, title: '續借服務', icon: 'refresh-outline', description: '延長圖書借閱期限' },
    { id: 5, title: '座位預約', icon: 'desktop-outline', description: '預約圖書館閱讀座位' },
    { id: 6, title: '開放時間', icon: 'time-outline', description: '查看圖書館開放時間' },
  ];

  const renderServiceItem = (service) => (
    <TouchableOpacity key={service.id} style={styles.serviceItem}>
      <View style={styles.serviceIcon}>
        <Ionicons name={service.icon} size={24} color="#364873" />
      </View>
      <View style={styles.serviceContent}>
        <Text style={styles.serviceTitle}>{service.title}</Text>
        <Text style={styles.serviceDescription}>{service.description}</Text>
      </View>
      <Ionicons name="chevron-forward" size={20} color="#ccc" />
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <Ionicons name="arrow-back" size={24} color="#333" style={styles.backIcon} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>圖書館</Text>
        </View>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Library Info Card */}
        <View style={styles.infoCard}>
          <View style={styles.infoHeader}>
            <Ionicons name="library-outline" size={32} color="#364873" />
            <Text style={styles.infoTitle}>校園圖書館</Text>
          </View>
          <Text style={styles.infoDescription}>
            提供豐富的學術資源和舒適的學習環境，支持您的學習和研究需求。
          </Text>
        </View>

        {/* Services Section */}
        <View style={styles.servicesSection}>
          <Text style={styles.sectionTitle}>圖書館服務</Text>
          <View style={styles.servicesList}>
            {libraryServices.map(renderServiceItem)}
          </View>
        </View>

        {/* Quick Info */}
        <View style={styles.quickInfoSection}>
          <Text style={styles.sectionTitle}>快速資訊</Text>
          <View style={styles.quickInfoCard}>
            <View style={styles.quickInfoItem}>
              <Text style={styles.quickInfoLabel}>今日開放時間</Text>
              <Text style={styles.quickInfoValue}>08:00 - 22:00</Text>
            </View>
            <View style={styles.quickInfoItem}>
              <Text style={styles.quickInfoLabel}>可用座位</Text>
              <Text style={styles.quickInfoValue}>156 / 200</Text>
            </View>
            <View style={styles.quickInfoItem}>
              <Text style={styles.quickInfoLabel}>聯絡電話</Text>
              <Text style={styles.quickInfoValue}>(02) 1234-5678</Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: 50,
    paddingBottom: 15,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  backIcon: {
    marginRight: 12,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
  },
  infoCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 20,
    marginTop: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  infoHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  infoTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginLeft: 12,
  },
  infoDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  servicesSection: {
    marginTop: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  servicesList: {
    backgroundColor: '#fff',
    borderRadius: 12,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  serviceItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  serviceIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f8f9fa',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  serviceContent: {
    flex: 1,
  },
  serviceTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  serviceDescription: {
    fontSize: 14,
    color: '#666',
  },
  quickInfoSection: {
    marginTop: 24,
    marginBottom: 20,
  },
  quickInfoCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  quickInfoItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  quickInfoLabel: {
    fontSize: 14,
    color: '#666',
  },
  quickInfoValue: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
  },
});

export default LibraryScreen;
