// API 配置文件
const API_CONFIG = {
  // 開發環境 API URL - 請根據您的後端服務器修改
  BASE_URL: 'https://73d80fb7982d.ngrok-free.app',
  
  // 生產環境可以使用
  // BASE_URL: 'https://api.lhu.edu.tw',
  
  ENDPOINTS: {
    REPAIRS: '/repairs',
    AUTH: '/auth',
    USERS: '/users',
  },
  
  // 請求超時時間（毫秒）
  TIMEOUT: 10000,
};

// 通用 API 請求函數
export const apiRequest = async (endpoint, options = {}) => {
  const url = `${API_CONFIG.BASE_URL}${endpoint}`;
  
  const defaultOptions = {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      // 如果需要認證，可以在這裡添加 Authorization header
      // 'Authorization': `Bearer ${getAuthToken()}`,
    },
    timeout: API_CONFIG.TIMEOUT,
  };

  const config = {
    ...defaultOptions,
    ...options,
    headers: {
      ...defaultOptions.headers,
      ...options.headers,
    },
  };

  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), config.timeout);
    
    const response = await fetch(url, {
      ...config,
      signal: controller.signal,
    });
    
    clearTimeout(timeoutId);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    if (error.name === 'AbortError') {
      throw new Error('請求超時，請檢查網路連線');
    }
    throw error;
  }
};

// 報修相關 API
export const repairAPI = {
  // 提交報修申請
  submitRepair: async (repairData) => {
    return await apiRequest(API_CONFIG.ENDPOINTS.REPAIRS, {
      method: 'POST',
      body: JSON.stringify(repairData),
    });
  },

  // 獲取報修記錄
  getRepairs: async (userId) => {
    return await apiRequest(`${API_CONFIG.ENDPOINTS.REPAIRS}?userId=${userId}`);
  },

  // 獲取報修詳情
  getRepairDetail: async (repairId) => {
    return await apiRequest(`${API_CONFIG.ENDPOINTS.REPAIRS}/${repairId}`);
  },

  // 更新報修狀態
  updateRepairStatus: async (repairId, status) => {
    return await apiRequest(`${API_CONFIG.ENDPOINTS.REPAIRS}/${repairId}`, {
      method: 'PUT',
      body: JSON.stringify({ status }),
    });
  },
};

// 圖片上傳 API
export const uploadImage = async (imageUri) => {
  try {
    const formData = new FormData();
    formData.append('image', {
      uri: imageUri,
      type: 'image/jpeg',
      name: 'repair_image.jpg',
    });

    const response = await fetch(`${API_CONFIG.BASE_URL}/upload`, {
      method: 'POST',
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      body: formData,
    });

    if (!response.ok) {
      throw new Error('圖片上傳失敗');
    }

    const data = await response.json();
    return data.imageUrl;
  } catch (error) {
    throw new Error('圖片上傳失敗: ' + error.message);
  }
};

export default API_CONFIG;
