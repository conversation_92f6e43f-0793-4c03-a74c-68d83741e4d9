// API 配置文件
const API_CONFIG = {
  // 開發環境 API URL - 請根據您的後端服務器修改
  // 如果沒有後端，可以使用模擬 API
  BASE_URL: 'https://73d80fb7982d.ngrok-free.app/repairs',

  // 生產環境可以使用
  // BASE_URL: 'https://api.lhu.edu.tw',

  // 模擬模式 - 當後端不可用時使用
  MOCK_MODE: true,

  ENDPOINTS: {
    REPAIRS: '/repairs',
    AUTH: '/auth',
    USERS: '/users',
  },

  // 請求超時時間（毫秒）
  TIMEOUT: 10000,
};

// 模擬 API 回應
const mockApiResponse = async (endpoint, options = {}) => {
  console.log('使用模擬 API:', endpoint, options);

  // 模擬網路延遲
  await new Promise(resolve => setTimeout(resolve, 1000));

  if (endpoint === API_CONFIG.ENDPOINTS.REPAIRS && options.method === 'POST') {
    return {
      success: true,
      message: '報修申請已提交（模擬模式）',
      repairId: `REP-${Date.now()}`,
      data: JSON.parse(options.body || '{}'),
    };
  }

  return {
    success: true,
    message: '模擬 API 回應',
    data: {},
  };
};

// 通用 API 請求函數
export const apiRequest = async (endpoint, options = {}) => {
  // 如果啟用模擬模式，直接返回模擬數據
  if (API_CONFIG.MOCK_MODE) {
    return await mockApiResponse(endpoint, options);
  }

  const url = `${API_CONFIG.BASE_URL}${endpoint}`;

  console.log('API 請求:', {
    url,
    method: options.method || 'GET',
    headers: options.headers,
  });

  const defaultOptions = {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      // 如果需要認證，可以在這裡添加 Authorization header
      // 'Authorization': `Bearer ${getAuthToken()}`,
    },
    timeout: API_CONFIG.TIMEOUT,
  };

  const config = {
    ...defaultOptions,
    ...options,
    headers: {
      ...defaultOptions.headers,
      ...options.headers,
    },
  };

  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), config.timeout);

    const response = await fetch(url, {
      ...config,
      signal: controller.signal,
    });

    clearTimeout(timeoutId);

    console.log('API 回應狀態:', response.status);

    if (!response.ok) {
      let errorMessage = `HTTP error! status: ${response.status}`;

      switch (response.status) {
        case 404:
          errorMessage = '找不到 API 端點，請檢查後端服務器是否啟動';
          break;
        case 500:
          errorMessage = '服務器內部錯誤';
          break;
        case 403:
          errorMessage = '權限不足';
          break;
        case 401:
          errorMessage = '未授權，請重新登入';
          break;
        default:
          errorMessage = `請求失敗 (${response.status})`;
      }

      throw new Error(errorMessage);
    }

    const data = await response.json();
    console.log('API 回應數據:', data);
    return data;
  } catch (error) {
    console.error('API 請求錯誤:', error);

    if (error.name === 'AbortError') {
      throw new Error('請求超時，請檢查網路連線');
    }

    if (error.message.includes('Network request failed')) {
      throw new Error('網路連線失敗，請檢查網路設定');
    }

    throw error;
  }
};

// 報修相關 API
export const repairAPI = {
  // 提交報修申請
  submitRepair: async (repairData) => {
    try {
      console.log('提交報修數據:', repairData);

      const response = await apiRequest(API_CONFIG.ENDPOINTS.REPAIRS, {
        method: 'POST',
        body: JSON.stringify(repairData),
      });

      console.log('報修提交回應:', response);
      return response;
    } catch (error) {
      console.error('報修提交失敗:', error);

      // 如果是 404 錯誤且不是模擬模式，建議啟用模擬模式
      if (error.message.includes('404') && !API_CONFIG.MOCK_MODE) {
        throw new Error('後端服務器未啟動，請聯絡系統管理員或啟用模擬模式進行測試');
      }

      throw error;
    }
  },

  // 獲取報修記錄
  getRepairs: async (userId) => {
    try {
      return await apiRequest(`${API_CONFIG.ENDPOINTS.REPAIRS}?userId=${userId}`);
    } catch (error) {
      console.error('獲取報修記錄失敗:', error);
      throw error;
    }
  },

  // 獲取報修詳情
  getRepairDetail: async (repairId) => {
    try {
      return await apiRequest(`${API_CONFIG.ENDPOINTS.REPAIRS}/${repairId}`);
    } catch (error) {
      console.error('獲取報修詳情失敗:', error);
      throw error;
    }
  },

  // 更新報修狀態
  updateRepairStatus: async (repairId, status) => {
    try {
      return await apiRequest(`${API_CONFIG.ENDPOINTS.REPAIRS}/${repairId}`, {
        method: 'PUT',
        body: JSON.stringify({ status }),
      });
    } catch (error) {
      console.error('更新報修狀態失敗:', error);
      throw error;
    }
  },
};

// 圖片上傳 API
export const uploadImage = async (imageUri) => {
  try {
    const formData = new FormData();
    formData.append('image', {
      uri: imageUri,
      type: 'image/jpeg',
      name: 'repair_image.jpg',
    });

    const response = await fetch(`${API_CONFIG.BASE_URL}/upload`, {
      method: 'POST',
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      body: formData,
    });

    if (!response.ok) {
      throw new Error('圖片上傳失敗');
    }

    const data = await response.json();
    return data.imageUrl;
  } catch (error) {
    throw new Error('圖片上傳失敗: ' + error.message);
  }
};

export default API_CONFIG;
