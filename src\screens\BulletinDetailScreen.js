import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

const BulletinDetailScreen = ({ navigation, route }) => {
  const { bulletin, category } = route.params;

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Ionicons name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>{category}</Text>
        <TouchableOpacity>
          <Ionicons name="notifications-outline" size={24} color="#fff" />
        </TouchableOpacity>
      </View>

      {/* Content */}
      <ScrollView style={styles.content}>
        <View style={styles.bulletinContainer}>
          {/* Title */}
          <Text style={styles.bulletinTitle}>{bulletin.title}</Text>
          
          {/* Meta Info */}
          <View style={styles.metaInfo}>
            <Text style={styles.metaText}>發布日期：{bulletin.date}</Text>
            <View style={[
              styles.statusBadge,
              { backgroundColor: bulletin.status === '重要' ? '#FF4444' : '#364873' }
            ]}>
              <Text style={styles.statusText}>{bulletin.status}</Text>
            </View>
          </View>

          {/* Content */}
          <View style={styles.bulletinContent}>
            <Text style={styles.contentText}>
              {bulletin.content}
            </Text>
            
            {/* Extended content for demonstration */}
            <Text style={styles.contentText}>
              {'\n'}詳細內容：{'\n\n'}
              這是一則詳細的公告內容，包含了所有相關的重要資訊。
              請同學們仔細閱讀並遵循相關規定。{'\n\n'}
              
              如有任何疑問，請聯絡相關單位：{'\n'}
              • 電話：02-1234-5678{'\n'}
              • Email：<EMAIL>{'\n'}
              • 辦公室：行政大樓A101{'\n\n'}
              
              感謝您的配合！
            </Text>
          </View>

          {/* Actions */}
          <View style={styles.actionsContainer}>
            <TouchableOpacity style={styles.actionButton}>
              <Ionicons name="bookmark-outline" size={20} color="#364873" />
              <Text style={styles.actionText}>收藏</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.actionButton}>
              <Ionicons name="share-outline" size={20} color="#364873" />
              <Text style={styles.actionText}>分享</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.actionButton}>
              <Ionicons name="download-outline" size={20} color="#364873" />
              <Text style={styles.actionText}>下載</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#364873',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: 50,
    paddingBottom: 15,
  },
  headerTitle: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
  },
  bulletinContainer: {
    backgroundColor: '#fff',
    margin: 16,
    borderRadius: 8,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  bulletinTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
    lineHeight: 28,
  },
  metaInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  metaText: {
    fontSize: 14,
    color: '#666',
  },
  statusBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  statusText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  bulletinContent: {
    marginBottom: 24,
  },
  contentText: {
    fontSize: 16,
    color: '#333',
    lineHeight: 24,
  },
  actionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  actionButton: {
    alignItems: 'center',
    paddingVertical: 8,
  },
  actionText: {
    fontSize: 12,
    color: '#364873',
    marginTop: 4,
    fontWeight: '500',
  },
});

export default BulletinDetailScreen;
