{"name": "campusapp", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "prebuild": "expo prebuild", "android-build": "expo run:android"}, "dependencies": {"@react-navigation/bottom-tabs": "^7.4.2", "@react-navigation/native": "^7.1.14", "@react-navigation/stack": "^7.4.2", "expo": "~53.0.17", "expo-image-picker": "^16.1.4", "expo-status-bar": "~2.2.3", "node-fetch": "^3.3.2", "react": "19.0.0", "react-native": "0.79.5", "react-native-gesture-handler": "~2.24.0", "react-native-html-parser": "^0.1.0", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "^4.11.1", "react-native-vector-icons": "^10.2.0"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}