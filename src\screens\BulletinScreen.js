import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  FlatList,
  Image,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

const BulletinScreen = ({ navigation }) => {
  const [activeTab, setActiveTab] = useState('推薦資訊');

  const tabs = ['推薦資訊', '系所公告', '校園公告'];

  const bulletinData = {
    '推薦資訊': [
      {
        id: 1,
        title: '校園資訊管理系統更新',
        date: '2025-10-10',
        category: '系統',
        isNew: true,
      },
      {
        id: 2,
        title: '學期末考試時間安排',
        date: '2025-10-10',
        category: '考試',
        isNew: true,
      },
      {
        id: 3,
        title: '圖書館開放時間調整',
        date: '2025-10-10',
        category: '通知',
        isNew: false,
      },
      {
        id: 4,
        title: '校園網路維護通知',
        date: '2025-10-10',
        category: '維護',
        isNew: false,
      },
    ],
    '系所公告': [
      {
        id: 1,
        title: '大三實習課程說明會',
        date: '2025-10-10',
        category: '實習',
        isNew: true,
      },
      {
        id: 2,
        title: '畢業專題發表會',
        date: '2025-10-10',
        category: '專題',
        isNew: true,
      },
      {
        id: 3,
        title: '系所課程異動通知',
        date: '2025-10-10',
        category: '課程',
        isNew: false,
      },
      {
        id: 4,
        title: 'Python程式設計競賽',
        date: '2025-10-10',
        category: '競賽',
        isNew: true,
      },
    ],
    '校園公告': [
      {
        id: 1,
        title: '校慶活動籌備',
        date: '2025-10-10',
        category: '活動',
        isNew: true,
      },
      {
        id: 2,
        title: '獎學金申請開始',
        date: '2025-10-10',
        category: '獎學金',
        isNew: true,
      },
      {
        id: 3,
        title: '學生宿舍申請',
        date: '2025-10-10',
        category: '住宿',
        isNew: false,
      },
      {
        id: 4,
        title: '校園安全宣導',
        date: '2025-10-10',
        category: '安全',
        isNew: false,
      },
    ],
  };

  const renderBulletinItem = ({ item }) => (
    <TouchableOpacity
      style={styles.bulletinItem}
      onPress={() => navigation.navigate('BulletinDetail', { bulletin: item, category: activeTab })}
    >
      <View style={styles.bulletinHeader}>
        <Text style={styles.bulletinTitle}>{item.title}</Text>
        {item.isNew && <View style={styles.newBadge}><Text style={styles.newText}>新</Text></View>}
      </View>
      <View style={styles.bulletinMeta}>
        <Text style={styles.bulletinDate}>{item.date}</Text>
        <Text style={styles.bulletinCategory}>{item.category}</Text>
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <Image
            source={require('../../assets/images/lhu-logo.png')}
            style={styles.schoolIcon}
            resizeMode="contain"
          />
          <Text style={styles.greeting}>Hi,User!</Text>
        </View>
        <View style={styles.headerIcons}>
          <TouchableOpacity style={styles.headerIcon}>
            <Ionicons name="notifications-outline" size={24} color="#fff" />
          </TouchableOpacity>
        </View>
      </View>

      {/* Tab Navigation */}
      <View style={styles.tabContainer}>
        {tabs.map((tab) => (
          <TouchableOpacity
            key={tab}
            style={[
              styles.tabButton,
              activeTab === tab && styles.activeTabButton,
            ]}
            onPress={() => setActiveTab(tab)}
          >
            <Text
              style={[
                styles.tabText,
                activeTab === tab && styles.activeTabText,
              ]}
            >
              {tab}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <View style={styles.searchBar}>
          <Text style={styles.searchIcon}>🔍</Text>
          <Text style={styles.searchPlaceholder}>以關鍵字搜尋</Text>
          <TouchableOpacity style={styles.filterButton}>
            <Text style={styles.filterText}>篩選 ▼</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Bulletin List */}
      <View style={styles.bulletinContainer}>
        <FlatList
          data={bulletinData[activeTab]}
          renderItem={renderBulletinItem}
          keyExtractor={(item) => item.id.toString()}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.bulletinList}
        />
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  header: {
    backgroundColor: '#364873',
    paddingHorizontal: 16,
    paddingTop: 50,
    paddingBottom: 15,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  schoolIcon: {
    width: 24,
    height: 24,
    marginRight: 8,
  },
  greeting: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  headerIcons: {
    flexDirection: 'row',
    gap: 15,
  },
  headerIcon: {
    padding: 5,
  },
  notificationIcon: {
    padding: 5,
  },
  notificationText: {
    fontSize: 20,
    color: '#FFFFFF',
  },
  menuIcon: {
    padding: 5,
  },
  menuText: {
    fontSize: 20,
    color: '#FFFFFF',
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 20,
    paddingVertical: 10,
  },
  tabButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  activeTabButton: {
    borderBottomColor: '#FF6B35',
  },
  tabText: {
    fontSize: 14,
    color: '#8E8E93',
    fontWeight: '500',
  },
  activeTabText: {
    color: '#FF6B35',
    fontWeight: 'bold',
  },
  searchContainer: {
    paddingHorizontal: 20,
    paddingVertical: 15,
    backgroundColor: '#FFFFFF',
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F8F9FA',
    borderRadius: 8,
    paddingHorizontal: 15,
    paddingVertical: 12,
  },
  searchIcon: {
    fontSize: 16,
    marginRight: 10,
  },
  searchPlaceholder: {
    flex: 1,
    fontSize: 14,
    color: '#8E8E93',
  },
  filterButton: {
    paddingHorizontal: 10,
  },
  filterText: {
    fontSize: 12,
    color: '#364873',
  },
  bulletinContainer: {
    flex: 1,
    paddingHorizontal: 20,
  },
  bulletinList: {
    paddingBottom: 20,
  },
  bulletinItem: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    padding: 15,
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  bulletinHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  bulletinTitle: {
    flex: 1,
    fontSize: 14,
    fontWeight: '500',
    color: '#333333',
    lineHeight: 20,
  },
  newBadge: {
    backgroundColor: '#FF6B35',
    borderRadius: 10,
    paddingHorizontal: 6,
    paddingVertical: 2,
    marginLeft: 8,
  },
  newText: {
    fontSize: 10,
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  bulletinMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  bulletinDate: {
    fontSize: 12,
    color: '#8E8E93',
  },
  bulletinCategory: {
    fontSize: 12,
    color: '#364873',
    fontWeight: '500',
  },
});

export default BulletinScreen;
