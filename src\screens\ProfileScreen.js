import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Alert,
  Image,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

const ProfileScreen = ({ navigation }) => {
  const handleMenuPress = (menuItem) => {
    if (menuItem === '登出') {
      navigation.reset({
        index: 0,
        routes: [{ name: 'Login' }],
      });
      return;
    }
    Alert.alert('功能', `即將開啟 ${menuItem} 功能`);
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <Image
            source={require('../../assets/images/lhu-logo.png')}
            style={styles.schoolIcon}
            resizeMode="contain"
          />
          <Text style={styles.greeting}>Hi,User!</Text>
        </View>
        <View style={styles.headerIcons}>
          <TouchableOpacity style={styles.headerIcon}>
            <Ionicons name="notifications-outline" size={24} color="#fff" />
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Profile Section */}
        <View style={styles.profileSection}>
          <View style={styles.profileCard}>
            <View style={styles.profileInfo}>
              <View style={styles.avatar}>
                <Ionicons name="person" size={40} color="#999" />
              </View>
              <View style={styles.userInfo}>
                <Text style={styles.userName}>王小明</Text>
                <Text style={styles.userDetail}>資訊管理系 三年級A班</Text>
                <Text style={styles.userDetail}>學號：D11042410XX</Text>
              </View>
              <TouchableOpacity style={styles.qrButton}>
                <Ionicons name="qr-code-outline" size={24} color="#666" />
              </TouchableOpacity>
            </View>
          </View>
        </View>

        {/* Points Section */}
        <View style={styles.pointsSection}>
          <View style={styles.pointsCard}>
            <View style={styles.pointsInfo}>
              <Text style={styles.pointsLabel}>我的點數</Text>
              <Text style={styles.pointsSubLabel}>查看歷史</Text>
            </View>
            <View style={styles.pointsRight}>
              <View style={styles.pointsDisplay}>
                <Ionicons name="card-outline" size={20} color="#666" />
                <Text style={styles.pointsValue}>0</Text>
              </View>
              <TouchableOpacity style={styles.redeemButton}>
                <Text style={styles.redeemButtonText}>兌換好禮</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>

        {/* Settings Section */}
        <View style={styles.settingsSection}>
          <Text style={styles.sectionTitle}>設定與編輯</Text>
          
          <TouchableOpacity style={styles.menuItem} onPress={() => handleMenuPress('設定')}>
            <View style={styles.menuItemLeft}>
              <Ionicons name="settings-outline" size={20} color="#666" />
              <Text style={styles.menuText}>設定</Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color="#C7C7CC" />
          </TouchableOpacity>

          <TouchableOpacity style={styles.menuItem} onPress={() => handleMenuPress('語言')}>
            <View style={styles.menuItemLeft}>
              <Ionicons name="language-outline" size={20} color="#666" />
              <Text style={styles.menuText}>語言</Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color="#C7C7CC" />
          </TouchableOpacity>
        </View>

        {/* Collaboration Section */}
        <View style={styles.collaborationSection}>
          <Text style={styles.sectionTitle}>協助與支援</Text>

          <TouchableOpacity style={styles.menuItem} onPress={() => handleMenuPress('遺失物尋找')}>
            <View style={styles.menuItemLeft}>
              <Ionicons name="search-outline" size={20} color="#666" />
              <Text style={styles.menuText}>遺失物尋找</Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color="#C7C7CC" />
          </TouchableOpacity>

          <TouchableOpacity style={styles.menuItem} onPress={() => handleMenuPress('使用教學')}>
            <View style={styles.menuItemLeft}>
              <Ionicons name="help-circle-outline" size={20} color="#666" />
              <Text style={styles.menuText}>使用教學</Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color="#C7C7CC" />
          </TouchableOpacity>

          <TouchableOpacity style={styles.menuItem} onPress={() => handleMenuPress('意見反饋')}>
            <View style={styles.menuItemLeft}>
              <Ionicons name="chatbubble-outline" size={20} color="#666" />
              <Text style={styles.menuText}>意見反饋</Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color="#C7C7CC" />
          </TouchableOpacity>

          <TouchableOpacity style={styles.menuItem} onPress={() => handleMenuPress('登出')}>
            <View style={styles.menuItemLeft}>
              <Ionicons name="log-out-outline" size={20} color="#FF3B30" />
              <Text style={[styles.menuText, { color: '#FF3B30' }]}>登出</Text>
            </View>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2F2F7',
  },
  header: {
    backgroundColor: '#364873',
    paddingHorizontal: 16,
    paddingTop: 50,
    paddingBottom: 15,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  schoolIcon: {
    width: 24,
    height: 24,
    marginRight: 8,
  },
  greeting: {
    color: '#fff',
    fontSize: 18,
    fontWeight: '600',
  },
  headerIcons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerIcon: {
    marginLeft: 15,
  },
  content: {
    flex: 1,
    backgroundColor: '#F2F2F7',
  },
  profileSection: {
    marginTop: 0,
    backgroundColor: '#fff',
  },
  profileCard: {
    paddingHorizontal: 20,
    paddingVertical: 20,
  },
  profileInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#F2F2F7',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000',
    marginBottom: 4,
  },
  userDetail: {
    fontSize: 14,
    color: '#666',
    marginBottom: 2,
  },
  qrButton: {
    padding: 8,
  },
  pointsSection: {
    backgroundColor: '#fff',
    marginTop: 10,
  },
  pointsCard: {
    paddingHorizontal: 20,
    paddingVertical: 20,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  pointsInfo: {
    flex: 1,
  },
  pointsLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000',
    marginBottom: 4,
  },
  pointsSubLabel: {
    fontSize: 14,
    color: '#007AFF',
  },
  pointsRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  pointsDisplay: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 15,
  },
  pointsValue: {
    fontSize: 24,
    fontWeight: '600',
    color: '#000',
    marginLeft: 8,
  },
  redeemButton: {
    backgroundColor: '#FF8C00',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 16,
  },
  redeemButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  settingsSection: {
    backgroundColor: '#fff',
    marginTop: 10,
  },
  collaborationSection: {
    backgroundColor: '#fff',
    marginTop: 10,
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000',
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 10,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 0.5,
    borderBottomColor: '#E5E5EA',
  },
  menuItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  menuText: {
    fontSize: 16,
    color: '#000',
    marginLeft: 12,
  },
});

export default ProfileScreen;
