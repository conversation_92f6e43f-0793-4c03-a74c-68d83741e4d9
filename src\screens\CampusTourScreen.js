import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

const CampusTourScreen = ({ navigation }) => {
  const [selectedView, setSelectedView] = useState('map');

  const buildings = [
    { id: 1, name: '圖書館', floor: 'B1', room: 'B101' },
    { id: 2, name: '學生活動中心', floor: '1F', room: '101' },
    { id: 3, name: '體育館', floor: '2F', room: '201' },
    { id: 4, name: '餐廳', floor: '1F', room: '102' },
    { id: 5, name: '宿舍', floor: '3F', room: '301' },
    { id: 6, name: '教學大樓', floor: '4F', room: '401' },
  ];

  const floorPlan = [
    { id: 1, room: 'A', floor: '1F' },
    { id: 2, room: 'B', floor: '1F' },
    { id: 3, room: 'C', floor: '2F' },
    { id: 4, room: 'D', floor: '2F' },
    { id: 5, room: 'E', floor: '3F' },
    { id: 6, room: 'F', floor: '3F' },
  ];

  const renderMapView = () => (
    <View style={styles.mapContainer}>
      <View style={styles.searchContainer}>
        <View style={styles.searchBar}>
          <Ionicons name="search" size={16} color="#8E8E93" style={styles.searchIcon} />
          <Text style={styles.searchPlaceholder}>搜尋校園位置</Text>
        </View>
      </View>

      <View style={styles.campusMapContainer}>
        <View style={styles.campusMap}>
          <Text style={styles.mapTitle}>校園地圖</Text>
          <Text style={styles.mapSubtitle}>點擊建築物查看詳細資訊</Text>
          
          {/* Campus Buildings */}
          <View style={styles.buildingsContainer}>
            {buildings.map((building) => (
              <TouchableOpacity
                key={building.id}
                style={styles.buildingItem}
              >
                <Text style={styles.buildingName}>{building.name}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </View>

      <View style={styles.navigationHint}>
        <Text style={styles.navigationIcon}>🧭</Text>
        <Text style={styles.navigationText}>點擊建築物獲取導航</Text>
      </View>
    </View>
  );

  const renderFloorPlanView = () => (
    <View style={styles.floorPlanContainer}>
      <View style={styles.searchContainer}>
        <View style={styles.searchBar}>
          <Text style={styles.searchIcon}>🔍</Text>
          <Text style={styles.searchPlaceholder}>搜尋樓層</Text>
        </View>
      </View>

      <View style={styles.floorHeader}>
        <Text style={styles.floorTitle}>N 棟 樓層平面圖</Text>
      </View>

      <View style={styles.floorControls}>
        <TouchableOpacity style={styles.floorButton}>
          <Text style={styles.floorButtonText}>▲</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.floorButton}>
          <Text style={styles.floorButtonText}>▼</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.floorMapContainer}>
        <View style={styles.floorMap}>
          <Text style={styles.floorMapTitle}>樓層平面圖</Text>
          <View style={styles.roomsGrid}>
            {floorPlan.map((room) => (
              <TouchableOpacity
                key={room.id}
                style={styles.roomItem}
              >
                <Text style={styles.roomText}>{room.room}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </View>

      <View style={styles.roomPhotoContainer}>
        <View style={styles.roomPhoto}>
          <Text style={styles.roomPhotoText}>房間照片顯示區域</Text>
        </View>
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Ionicons name="chevron-back" size={24} color="#FFFFFF" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>校園導覽</Text>
        <TouchableOpacity style={styles.notificationButton}>
          <Ionicons name="notifications-outline" size={24} color="#FFFFFF" />
        </TouchableOpacity>
      </View>

      {/* View Toggle */}
      <View style={styles.viewToggle}>
        <TouchableOpacity
          style={[
            styles.toggleButton,
            selectedView === 'map' && styles.activeToggleButton,
          ]}
          onPress={() => setSelectedView('map')}
        >
          <Text
            style={[
              styles.toggleText,
              selectedView === 'map' && styles.activeToggleText,
            ]}
          >
            校園地圖
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[
            styles.toggleButton,
            selectedView === 'floor' && styles.activeToggleButton,
          ]}
          onPress={() => setSelectedView('floor')}
        >
          <Text
            style={[
              styles.toggleText,
              selectedView === 'floor' && styles.activeToggleText,
            ]}
          >
            樓層平面圖
          </Text>
        </TouchableOpacity>
      </View>

      {/* Content */}
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {selectedView === 'map' ? renderMapView() : renderFloorPlanView()}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 50,
    paddingBottom: 15,
    backgroundColor: '#364873',
  },
  backButton: {
    padding: 5,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  notificationButton: {
    padding: 5,
  },
  viewToggle: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    margin: 15,
    borderRadius: 8,
    padding: 4,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  toggleButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderRadius: 6,
  },
  activeToggleButton: {
    backgroundColor: '#364873',
  },
  toggleText: {
    fontSize: 14,
    color: '#8E8E93',
    fontWeight: '500',
  },
  activeToggleText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  scrollView: {
    flex: 1,
  },
  searchContainer: {
    paddingHorizontal: 15,
    marginBottom: 15,
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 25,
    paddingHorizontal: 15,
    paddingVertical: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 3,
  },
  searchIcon: {
    marginRight: 10,
  },
  searchPlaceholder: {
    flex: 1,
    fontSize: 14,
    color: '#8E8E93',
  },
  mapContainer: {
    paddingBottom: 20,
  },
  campusMapContainer: {
    margin: 15,
  },
  campusMap: {
    backgroundColor: '#4CAF50',
    borderRadius: 12,
    padding: 20,
    minHeight: 300,
    justifyContent: 'center',
    alignItems: 'center',
  },
  mapTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 8,
  },
  mapSubtitle: {
    fontSize: 12,
    color: '#FFFFFF',
    marginBottom: 20,
  },
  buildingsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    gap: 10,
  },
  buildingItem: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    margin: 4,
  },
  buildingName: {
    fontSize: 12,
    color: '#FFFFFF',
    fontWeight: '500',
  },
  navigationHint: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 15,
  },
  navigationIcon: {
    fontSize: 20,
    marginRight: 8,
  },
  navigationText: {
    fontSize: 14,
    color: '#666666',
  },
  floorPlanContainer: {
    paddingBottom: 20,
  },
  floorHeader: {
    alignItems: 'center',
    marginBottom: 15,
  },
  floorTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333333',
  },
  floorControls: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 20,
    marginBottom: 15,
  },
  floorButton: {
    backgroundColor: '#364873',
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  floorButtonText: {
    fontSize: 16,
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  floorMapContainer: {
    margin: 15,
    marginBottom: 20,
  },
  floorMap: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  floorMapTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333333',
    textAlign: 'center',
    marginBottom: 20,
  },
  roomsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-around',
  },
  roomItem: {
    backgroundColor: '#E8EBF0',
    borderRadius: 8,
    width: 60,
    height: 60,
    justifyContent: 'center',
    alignItems: 'center',
    margin: 8,
    borderWidth: 1,
    borderColor: '#364873',
  },
  roomText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#364873',
  },
  roomPhotoContainer: {
    margin: 15,
  },
  roomPhoto: {
    backgroundColor: '#E5E5EA',
    borderRadius: 12,
    height: 150,
    justifyContent: 'center',
    alignItems: 'center',
  },
  roomPhotoText: {
    fontSize: 14,
    color: '#8E8E93',
  },
});

export default CampusTourScreen;
