import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Image,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

const FoodMapScreen = ({ navigation }) => {
  const [selectedCategory, setSelectedCategory] = useState('全部');

  const categories = [
    { id: 1, name: '飲料', icon: '🥤' },
    { id: 2, name: '飲品', icon: '🧋' },
    { id: 3, name: '麵食/小吃', icon: '🍜' },
    { id: 4, name: '主餐類', icon: '🍱' },
  ];

  const restaurants = [
    {
      id: 1,
      name: '河內小館',
      rating: '★★★★☆',
      price: '$$$',
      image: 'restaurant1',
    },
    {
      id: 2,
      name: 'COMEBUY',
      rating: '★★★★★',
      price: '$$',
      image: 'restaurant2',
    },
    {
      id: 3,
      name: '美味餐廳',
      rating: '★★★★☆',
      price: '$$$',
      image: 'restaurant3',
    },
  ];

  const renderCategoryItem = (category) => (
    <TouchableOpacity
      key={category.id}
      style={[
        styles.categoryItem,
        selectedCategory === category.name && styles.selectedCategoryItem,
      ]}
      onPress={() => setSelectedCategory(category.name)}
    >
      <Text style={styles.categoryIcon}>{category.icon}</Text>
      <Text
        style={[
          styles.categoryText,
          selectedCategory === category.name && styles.selectedCategoryText,
        ]}
      >
        {category.name}
      </Text>
    </TouchableOpacity>
  );

  const renderRestaurantItem = (restaurant) => (
    <TouchableOpacity key={restaurant.id} style={styles.restaurantItem}>
      <View style={styles.restaurantImageContainer}>
        <View style={styles.restaurantImagePlaceholder}>
          <Text style={styles.restaurantImageText}>圖片</Text>
        </View>
      </View>
      <View style={styles.restaurantInfo}>
        <Text style={styles.restaurantName}>{restaurant.name}</Text>
        <Text style={styles.restaurantRating}>{restaurant.rating}</Text>
        <Text style={styles.restaurantPrice}>{restaurant.price}</Text>
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Ionicons name="chevron-back" size={24} color="#FFFFFF" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>美食地圖</Text>
        <TouchableOpacity style={styles.notificationButton}>
          <Ionicons name="notifications-outline" size={24} color="#FFFFFF" />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Location Info */}
        <View style={styles.locationContainer}>
          <View style={styles.locationInfo}>
            <Text style={styles.locationIcon}>📍</Text>
            <View style={styles.locationText}>
              <Text style={styles.locationTitle}>目前位置</Text>
              <Text style={styles.locationAddress}>
                333桃園市龜山區樂善里樂善一街300號
              </Text>
            </View>
          </View>
        </View>

        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <View style={styles.searchBar}>
            <Ionicons name="search" size={16} color="#8E8E93" style={styles.searchIcon} />
            <Text style={styles.searchPlaceholder}>搜尋美食</Text>
          </View>
        </View>

        {/* Categories */}
        <View style={styles.categoriesContainer}>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.categoriesScrollView}
          >
            {categories.map(renderCategoryItem)}
          </ScrollView>
        </View>

        {/* Map Placeholder */}
        <View style={styles.mapContainer}>
          <View style={styles.mapPlaceholder}>
            <Text style={styles.mapText}>地圖顯示區域</Text>
            <Text style={styles.mapSubText}>顯示附近美食位置</Text>
          </View>
        </View>

        {/* Restaurants List */}
        <View style={styles.restaurantsContainer}>
          <Text style={styles.sectionTitle}>推薦美食</Text>
          <View style={styles.restaurantsList}>
            {restaurants.map(renderRestaurantItem)}
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#364873',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 50,
    paddingBottom: 15,
    backgroundColor: '#364873',
  },
  backButton: {
    padding: 5,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  notificationButton: {
    padding: 5,
  },
  scrollView: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  locationContainer: {
    backgroundColor: '#FFFFFF',
    margin: 15,
    borderRadius: 12,
    padding: 15,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  locationInfo: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  locationIcon: {
    fontSize: 20,
    marginRight: 10,
    marginTop: 2,
  },
  locationText: {
    flex: 1,
  },
  locationTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 4,
  },
  locationAddress: {
    fontSize: 12,
    color: '#666666',
    lineHeight: 18,
  },
  searchContainer: {
    paddingHorizontal: 15,
    marginBottom: 15,
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 25,
    paddingHorizontal: 15,
    paddingVertical: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 3,
  },
  searchIcon: {
    marginRight: 10,
  },
  searchPlaceholder: {
    flex: 1,
    fontSize: 14,
    color: '#8E8E93',
  },
  categoriesContainer: {
    marginBottom: 15,
  },
  categoriesScrollView: {
    paddingHorizontal: 15,
  },
  categoryItem: {
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 12,
    marginRight: 12,
    minWidth: 70,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 3,
  },
  selectedCategoryItem: {
    backgroundColor: '#364873',
  },
  categoryIcon: {
    fontSize: 24,
    marginBottom: 4,
  },
  categoryText: {
    fontSize: 12,
    color: '#333333',
    textAlign: 'center',
  },
  selectedCategoryText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  mapContainer: {
    margin: 15,
    marginBottom: 20,
  },
  mapPlaceholder: {
    height: 200,
    backgroundColor: '#E8EBF0',
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#364873',
    borderStyle: 'dashed',
  },
  mapText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#364873',
    marginBottom: 4,
  },
  mapSubText: {
    fontSize: 12,
    color: '#666666',
  },
  restaurantsContainer: {
    paddingHorizontal: 15,
    paddingBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 15,
  },
  restaurantsList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  restaurantItem: {
    width: '48%',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  restaurantImageContainer: {
    height: 100,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    overflow: 'hidden',
  },
  restaurantImagePlaceholder: {
    flex: 1,
    backgroundColor: '#E5E5EA',
    justifyContent: 'center',
    alignItems: 'center',
  },
  restaurantImageText: {
    fontSize: 14,
    color: '#8E8E93',
  },
  restaurantInfo: {
    padding: 12,
  },
  restaurantName: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 4,
  },
  restaurantRating: {
    fontSize: 12,
    color: '#FF9800',
    marginBottom: 2,
  },
  restaurantPrice: {
    fontSize: 12,
    color: '#666666',
  },
});

export default FoodMapScreen;
